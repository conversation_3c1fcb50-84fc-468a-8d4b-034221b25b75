#!/bin/bash

# 启动应用脚本

set -e

# 颜色定义
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# 输出带颜色的信息
info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

info "=== 启动Chat Tag System应用 ==="

# 设置Java 8环境
export JAVA_HOME=/usr/local/opt/openjdk@8/libexec/openjdk.jdk/Contents/Home
export PATH="/usr/local/opt/openjdk@8/bin:$PATH"

# 验证Java版本
info "验证Java版本..."
java -version

# 应用JAR文件路径
APP_JAR="chat-tag-system-start/target/chat-tag-system.jar"

# 检查JAR文件是否存在
if [ ! -f "$APP_JAR" ]; then
    error "JAR文件不存在: $APP_JAR"
    error "请先运行构建脚本: ./build-with-java8.sh"
    exit 1
fi

info "找到应用JAR: $APP_JAR"

# JVM参数
JVM_OPTS="-Xms1024m -Xmx2048m"

# Spring配置文件
SPRING_PROFILES="passport,test"

# 检查端口是否被占用
PORT=8080
if lsof -Pi :$PORT -sTCP:LISTEN -t >/dev/null ; then
    warn "端口 $PORT 已被占用"
    read -p "是否继续启动? (y/n): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        info "启动已取消"
        exit 0
    fi
fi

info "启动应用..."
info "JVM参数: $JVM_OPTS"
info "Spring配置: $SPRING_PROFILES"
info "应用将在端口 $PORT 启动"

# 启动应用
java $JVM_OPTS -jar "$APP_JAR" --spring.profiles.active=$SPRING_PROFILES

info "应用已停止"
