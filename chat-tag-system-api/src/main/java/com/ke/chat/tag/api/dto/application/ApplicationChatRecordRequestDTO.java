package com.ke.chat.tag.api.dto.application;

import com.ke.chat.tag.api.dto.evaluation.ChatEvaluation;
import com.ke.chat.tag.api.response.BasePageRequest;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ApplicationChatRecordRequestDTO extends BasePageRequest {
    private Long applicationId;

    private Integer source;

    private Integer isThumbsUp;

    private String status;

    private String query;

    private Integer isOwner;

    private String userName;

    private String userId;

    private String tag;

    private Long lastId;

    private Date lastCreateTime;

    private String muid;

    private ChatEvaluation evaluations;

    /**
     * 空间编码
     */
    private String spaceCode;


    /**
     * 开始时间
     */
    private Date timeStart;

    /**
     * 结束时间
     */
    private Date timeEnd;

    /**
     * 提问可用性
     */
    private List<String> questionEvaluationResults;
    /**
     * 回答可用性
     */
    private List<String> replyEvaluationResults;

}
