package com.ke.chat.tag.api.dto.application;

import lombok.AllArgsConstructor;
import lombok.Data;

import javax.annotation.Nullable;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
public class ApplicationStatisticsCollectReqDTO {

    @Nullable
    private List<Long> applicationIds;

    @NotEmpty(message = "spaceCode不能为空")
    private String spaceCode;

}
