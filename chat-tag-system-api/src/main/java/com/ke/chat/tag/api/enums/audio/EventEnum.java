package com.ke.chat.tag.api.enums.audio;

import lombok.Data;
import lombok.Getter;

@Getter
public enum EventEnum {

    // 客户端事件
    SESSION_UPDATE("session.update", "更新session配置"),
    CONVERSATION_ITEM_CREATE("conversation.item.create", "创建item"),
    CONVERSATION_ITEM_TRUNCATE("conversation.item.truncate", "语音打断"),
    INPUT_AUDIO_BUFFER_APPEND("input_audio_buffer.append", "语音数据追加"),
    INPUT_AUDIO_BUFFER_COMMIT("input_audio_buffer.commit", "语音数据发送完成"),
    RESPONSE_CANCEL("response.cancel", "服务端取消当前任务"),
    RESPONSE_CREATE("response.create", "服务端创建响应"),

    //服务端事件
    CONVERSATION_CREATED("conversation.created", "服务端创建会话"),
    CONVERSATION_ITEM_CREATED("conversation.item.created", "已创建item"),
    CONVERSATION_ITEM_TRUNCATED("conversation.item.truncated", "服务端响应打断"),
    ERROR("error", "服务端响应错误"),
    INPUT_AUDIO_BUFFER_COMMITTED("input_audio_buffer.committed", "服务端响应音频提交"),
    CONVERSATION_ITEM_INPUT_AUDIO_TRANSCRIPTION_COMPLETED("conversation.item.input_audio_transcription.completed", "服务端响应asr完成"),
    INPUT_AUDIO_TRANSCRIPT_DELTA("input_audio_transcript.delta", "asr中间数据"),
    INPUT_AUDIO_TRANSCRIPT_DONE("input_audio_transcript.done", "asr完成"),
    RESPONSE_AUDIO_DELTA("response.audio.delta", "tts音频数据"),
    RESPONSE_AUDIO_DONE("response.audio.done", "tts音频数据完成"),
    RESPONSE_AUDIO_TRANSCRIPT_DELTA("response.audio_transcript.delta", "音频转录数据"),
    RESPONSE_AUDIO_TRANSCRIPT_DONE("response.audio_transcript.done", "音频转录完成"),
    RESPONSE_CREATED("response.created", "响应创建"),
    RESPONSE_DONE("response.done", "响应完成"),
    RESPONSE_TEXT_DELTA("response.text.delta", "文本数据"),
    RESPONSE_TEXT_DONE("response.text.done", "文本输出完成"),
    SESSION_CREATED("session.created", "创建session"),
    SESSION_UPDATED("session.updated", "更新session"),

    ;



    @Getter
    public final String type;

    public final String desc;

    EventEnum(String type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public static EventEnum getEventByType(String type) {
        for (EventEnum event : EventEnum.values()) {
            if (event.getType().equals(type)) {
                return event;
            }
        }
        return null;
    }
}
