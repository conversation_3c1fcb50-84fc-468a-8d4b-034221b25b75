package com.ke.chat.tag.api.dto.knowledge;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class KnowledgeQaPairListDTO {
    @NotEmpty
    private List<KnowledgeQaPair> qaPairList;
    private Integer totalNum;
}
