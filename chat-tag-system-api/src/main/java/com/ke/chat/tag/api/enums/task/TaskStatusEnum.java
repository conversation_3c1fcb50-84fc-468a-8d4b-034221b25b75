package com.ke.chat.tag.api.enums.task;

import java.util.Arrays;
import java.util.List;

public enum TaskStatusEnum {

    TIMEOUT(3, "已过期"),
    DONE(2, "已完成"),
    SUBMITTED(1, "已提交"),
    PENDING(0, "待处理"),
    READY_ASSIGN(-1, "待分配");

    public final int code;
    public final String desc;

    TaskStatusEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    // 待分配
    public static final List<Integer> readAssignEnumList = Arrays.asList(READY_ASSIGN.getCode());
    // 改派
    public static final List<Integer> reAssignEnumList = Arrays.asList(PENDING.getCode(), TIMEOUT.getCode());

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
