package com.ke.chat.tag.api.dto.knowledge;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class KnowledgeChunkInfo {

    /**
     * 切片序号
     */
    private Integer order;

    /**
     * 切片类型
     */
    private String chunkType;

    /**
     * 标题行
     * type为qa时，显示提问；type为段落时，显示标题
     */
    private String headline;

    /**
     * 内容
     * type为qa时，显示回答；type为段落时，显示文本段
     */
    private String content;

    /**
     * 切片id
     */
    private String chunkId;

    private long chunkDataId;

    private int isOnline;

    private List<String> cityList;

    private List<String> imgList;

    private List<String> businessTags;


    private Integer chunkIndex;

    private Integer subIndex;
}
