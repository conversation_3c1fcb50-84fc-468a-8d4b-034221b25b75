package com.ke.chat.tag.api.dto.application;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.ke.chat.tag.api.response.BasePageResponse;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.List;

/**
 * function: 智能体sug搜索返回结果
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ApplicationSugRespDTO extends BasePageResponse {

    private List<ApplicationSugRespDetail> suggestList;


    @Data
    @SuperBuilder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ApplicationSugRespDetail{

        /**
         * 智能体id(外部id)
         */
        private Long applicationId;

        /**
         * 智能体id(内部id)，与前端交互时屏蔽掉此属性，近后端内部使用
         */
        @JsonIgnore
        private Long innerId;

        /**
         * 智能体名称
         */
        private String name;

        /**
         * 智能体描述
         */
        private String description;

        /**
         * 智能体icon
         */
        private String icon;

        /**
         * 智能体热度值描述
         */
        private String heat;

        /**
         * 智能体使用的模型名称
         */
        private String modelName;

        /**
         * 智能体支持最大输入字符数
         */
        private Integer inputLimit;


    }
}
