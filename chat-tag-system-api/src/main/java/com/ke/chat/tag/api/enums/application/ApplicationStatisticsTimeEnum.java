package com.ke.chat.tag.api.enums.application;

import lombok.Getter;

import java.time.DayOfWeek;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 应用数据统计维度枚举
 *
 * <AUTHOR>
 */
@Getter
public enum ApplicationStatisticsTimeEnum {
    LAST_ONE_WEEK("lastOneWeek", "过去一周", e -> e.minusDays(6).with(LocalTime.MIN), t -> t.plusDays(1)),
    LAST_TWO_WEEK("lastTwoWeek", "过去两周", e -> e.minusDays(13).with(LocalTime.MIN), e -> e.plusDays(1)),
    LAST_ONE_MONTH("lastOneMonth", "过去一个月", e -> e.minusDays(30).with(DayOfWeek.MONDAY).with(LocalTime.MIN), e -> e.plusWeeks(1)),
    LAST_HALF_YEAR("lastHalfYear", "过去半年", e -> e.minusMonths(6).with(DayOfWeek.MONDAY).with(LocalTime.MIN), e -> e.plusWeeks(1));

    private final String name;
    private final String desc;
    private final Function<LocalDateTime, LocalDateTime> statisticsBeginDay;
    private final Function<LocalDateTime, LocalDateTime> nextDay;

    ApplicationStatisticsTimeEnum(String name, String desc, Function<LocalDateTime, LocalDateTime> statisticsBeginDay, Function<LocalDateTime, LocalDateTime> nextDay) {
        this.name = name;
        this.desc = desc;
        this.statisticsBeginDay = statisticsBeginDay;
        this.nextDay = nextDay;
    }

    public static Map<String, ApplicationStatisticsTimeEnum> applicationStatisticsTimeEnumMap() {
        return Stream.of(ApplicationStatisticsTimeEnum.values()).collect(Collectors.toMap(ApplicationStatisticsTimeEnum::getName, Function.identity()));
    }
}
