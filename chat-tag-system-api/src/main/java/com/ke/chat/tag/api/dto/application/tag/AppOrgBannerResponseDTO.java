package com.ke.chat.tag.api.dto.application.tag;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @Author: gaopan034
 * @Date: 2024/11/4
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AppOrgBannerResponseDTO {
    private String business;

    private String desc;

    private int applicationCount;

    private long useCount;

    private List<TopApplicationInfo> topApplications;


    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TopApplicationInfo {
        private Long applicationId;
        private String name;
        private String icon;
        private String description;
        private Long heat;
    }
}
