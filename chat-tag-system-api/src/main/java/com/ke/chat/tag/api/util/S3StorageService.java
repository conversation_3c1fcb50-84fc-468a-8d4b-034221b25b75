package com.ke.chat.tag.api.util;

import com.amazonaws.AmazonServiceException;
import com.amazonaws.HttpMethod;
import com.amazonaws.SdkClientException;
import com.amazonaws.auth.AWSStaticCredentialsProvider;
import com.amazonaws.auth.BasicAWSCredentials;
import com.amazonaws.client.builder.AwsClientBuilder.EndpointConfiguration;
import com.amazonaws.regions.Regions;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.AmazonS3ClientBuilder;
import com.amazonaws.services.s3.model.GeneratePresignedUrlRequest;
import com.amazonaws.services.s3.model.ObjectMetadata;
import com.amazonaws.services.s3.model.ResponseHeaderOverrides;
import com.amazonaws.services.s3.model.S3Object;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FilenameUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.activation.MimetypesFileTypeMap;
import java.io.BufferedInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.UnsupportedEncodingException;
import java.net.HttpURLConnection;
import java.net.MalformedURLException;
import java.net.URL;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;

/**
 * S3存储服务
 *
 * <AUTHOR>
 * @date 2023/7/10 12:11 下午
 **/
@Service
@Slf4j
public class S3StorageService {


    private String endPoint;
    private String bucketName;
    private final AmazonS3 amazonS3;

    public S3StorageService(@Value("${chat.tag.ak}") String ak,
                            @Value("${chat.tag.sk}") String sk,
                            @Value("${s3.endPoint.inner}") String endPoint,
                            @Value("${s3.default.bucketName}") String bucketName) {
        this.endPoint = endPoint;
        this.bucketName = bucketName;
        BasicAWSCredentials credentials = new BasicAWSCredentials(ak, sk);
        AWSStaticCredentialsProvider provider = new AWSStaticCredentialsProvider(credentials);
        this.amazonS3 = AmazonS3ClientBuilder.standard().withCredentials(provider)
            .withEndpointConfiguration(new EndpointConfiguration(endPoint, Regions.CN_NORTH_1.getName()))
            .withPathStyleAccessEnabled(true).build();
    }


    public String putObject(String fileKey, InputStream inputStream, String contentType) {
        ObjectMetadata objectMetadata = new ObjectMetadata();
        objectMetadata.setContentType(contentType);
        amazonS3.putObject(bucketName, fileKey, inputStream, objectMetadata);
        return fileKey;
    }

    public String putObject(String fileKey, InputStream inputStream,
                            String contentType, String downloadFileName,
                            long contentLength) throws UnsupportedEncodingException {
        ObjectMetadata objectMetadata = new ObjectMetadata();
        objectMetadata.setContentType(contentType);
        objectMetadata.setContentLength(contentLength);
        downloadFileName = URLEncoder.encode(downloadFileName, StandardCharsets.UTF_8.toString()).replaceAll("\\+", "%20");
        objectMetadata.setContentDisposition("attachment;filename=\"" + downloadFileName + "\"" + ";filename*=utf-8''" + downloadFileName);
        amazonS3.putObject(bucketName, fileKey, inputStream, objectMetadata);
        return fileKey;
    }

    /**
     * 文件是否存在
     *
     * @param fileKey
     * @return
     */
    public boolean fileKeyExist(String fileKey) {
        return amazonS3.doesObjectExist(bucketName, fileKey);
    }

    public void deleteFile(String fileKey) {
        amazonS3.deleteObject(bucketName, fileKey);
    }

    /**
     * 文件重命名
     *
     * @param newFileKey
     * @param oldFileKey
     */
    public void reName(String oldFileKey, String newFileKey) {
        amazonS3.copyObject(bucketName, oldFileKey, bucketName, newFileKey);
        amazonS3.deleteObject(bucketName, oldFileKey);
    }

    // 生成存储服务地址
    public String signUrl(String fileKey, long expirationSeconds) {
        LOGGER.info("signUrl() fileKey:{}", fileKey);
        Date expirationDate = Date.from(LocalDateTime.now().plusSeconds(expirationSeconds).atZone(ZoneId.systemDefault()).toInstant());
        URL singedUrl = amazonS3.generatePresignedUrl(bucketName, fileKey, expirationDate);
        return singedUrl.toString();
    }

    /**
     * 重新生成签名(存储服务)
     *
     * @param fileUrl
     * @param expirationSeconds
     * @return
     * @throws MalformedURLException
     */
    public String reSign(String fileUrl, long expirationSeconds) throws MalformedURLException {
        LOGGER.info("reSign() fileUrl:{}", fileUrl);
        //fileUrl: http://storage.lianjia.com/cv-aigc/belle/f02e5db5c0e6423481506058aad31566741688435754_.jpg?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Date=20230710T061917Z&X-Amz-SignedHeaders=host&X-Amz-Expires=1439&X-Amz-Credential=Q265N5ELG32TT7UWO8YJ%2F20230710%2Fcn-north-1%2Fs3%2Faws4_request&X-Amz-Signature=30ef3d505db220deda01209e61446202f248b02eed7b8c00992a18b78064eb2c
        fileUrl = URLDecoder.decode(fileUrl);
        URL url = new URL(fileUrl);
        //提取文件路径
        //filePath: /cv-aigc/belle/f02e5db5c0e6423481506058aad31566741688435754_.jpg
        String filePath = url.getPath();
        Path path = Paths.get(filePath);
        //
        String fileKey = "";
        for (int i = 1; i < path.getNameCount(); i++) {
            fileKey = FilenameUtils.concat(fileKey, path.getName(i).toString());
        }
        //fileKey: belle/f02e5db5c0e6423481506058aad31566741688435754_.jpg
        return signUrl(fileKey, expirationSeconds);
    }

    public S3Object getFile(String key) {
        return getFile(bucketName, key);
    }

    private S3Object getFile(String bucket, String key) {
        long start = System.currentTimeMillis();
        S3Object s3Object = null;
        try {
            s3Object = amazonS3.getObject(bucket, key);
        } catch (AmazonServiceException e) {
            LOGGER.error("download S3 file AmazonServiceException.", e);
        } catch (SdkClientException e) {
            LOGGER.error("download S3 file SdkClientException.", e);
        }
        LOGGER.info("download S3 file : {}, time cost : {}", key, System.currentTimeMillis() - start);
        return s3Object;
    }

    /**
     * 从URL下载文件并上传到S3
     */
    public String uploadImageFromUrl(String fileUrl, String s3Key) {
        if (fileUrl == null || fileUrl.isEmpty() || s3Key == null || s3Key.isEmpty()) {
            LOGGER.error("Invalid URL or S3 key.");
            return null;
        }
        HttpURLConnection connection = null;
        try {
            URL url = new URL(fileUrl);
            connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod("GET");
            connection.connect();

            int responseCode = connection.getResponseCode();
            if (responseCode == HttpURLConnection.HTTP_OK) {
                try (InputStream inputStream = new BufferedInputStream(connection.getInputStream())) {
                    String contentType = connection.getContentType();
                    if (contentType == null || contentType.isEmpty()) {
                        contentType = new MimetypesFileTypeMap().getContentType(s3Key);
                    }
                    putObject(s3Key, inputStream, contentType);
                    return s3Key;
                }
            } else {
                LOGGER.error("Failed to download file from URL: {} with HTTP status: {}", fileUrl, responseCode);
            }
        } catch (MalformedURLException e) {
            LOGGER.error("Malformed URL: {}", fileUrl, e);
        } catch (IOException e) {
            LOGGER.error("IO Exception while downloading or uploading file from URL: {}", fileUrl, e);
        } finally {
            if (connection != null) {
                connection.disconnect();
            }
        }
        return null;
    }

    public String getFileSignedUrlWithCustomizeName(String fileKey, String outputFileName) {
        // 创建ResponseHeaderOverrides对象并设置Content-Disposition
        ResponseHeaderOverrides responseHeaders = new ResponseHeaderOverrides()
            .withContentDisposition("attachment; filename=\"" + outputFileName + "\"");

        // 创建GeneratePresignedUrlRequest对象
        Date expiration = new Date();
        long expTimeMillis = expiration.getTime();
        expTimeMillis += 60 * 24 * 1000;
        expiration.setTime(expTimeMillis);
        GeneratePresignedUrlRequest generatePresignedUrlRequest =
            new GeneratePresignedUrlRequest(bucketName, fileKey)
                .withMethod(HttpMethod.GET)
                .withExpiration(expiration)
                .withResponseHeaders(responseHeaders);

        try {
            URL url = amazonS3.generatePresignedUrl(generatePresignedUrlRequest);
            return url.toString();
        } catch (Exception e) {
            LOGGER.error("Failed to download file from URL: {}", fileKey, e);
        }
        return null;
    }

    public String removeSignature(String singedUrl) {
        if (org.apache.commons.lang3.StringUtils.isEmpty(singedUrl) || !singedUrl.contains("?")) {
            return singedUrl;
        }
        return singedUrl.substring(0, singedUrl.indexOf("?"));
    }
}
