package com.ke.chat.tag.api.util;

import javax.crypto.Cipher;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.util.Base64;

/**
 * AESUtil
 *
 * <AUTHOR>
 * @date 2025/3/4
 */
public class AESUtil {

    /**
     * 使用AES算法加密字符串
     *
     * @param data 要加密的原始字符串
     * @param key  16位的密钥（AES-128要求16个字符）
     * @return 加密后的Base64编码字符串
     * @throws Exception
     */
    public static String encrypt(String data, String key) throws Exception {
        if (key == null || key.length() != 16) {
            throw new IllegalArgumentException("密钥必须为16个字符");
        }
        // 获取Cipher实例，参数指定算法/模式/填充方式
        Cipher cipher = Cipher.getInstance("AES/ECB/PKCS5Padding");
        // 根据密钥构造SecretKeySpec对象
        SecretKeySpec secretKey = new SecretKeySpec(key.getBytes("UTF-8"), "AES");
        // 初始化为加密模式
        cipher.init(Cipher.ENCRYPT_MODE, secretKey);
        // 执行加密操作
        byte[] encryptedBytes = cipher.doFinal(data.getBytes("UTF-8"));
        // 返回Base64编码后的加密字符串
        return Base64.getEncoder().encodeToString(encryptedBytes);
    }

    /**
     * 使用AES算法解密字符串
     *
     * @param encryptedData 加密后的Base64编码字符串
     * @param key           16位的密钥
     * @return 解密后的原始字符串
     * @throws Exception
     */
    public static String decrypt(String encryptedData, String key) throws Exception {
        if (key == null || key.length() != 16) {
            throw new IllegalArgumentException("密钥必须为16个字符");
        }
        Cipher cipher = Cipher.getInstance("AES/ECB/PKCS5Padding");
        SecretKeySpec secretKey = new SecretKeySpec(key.getBytes(StandardCharsets.UTF_8), "AES");
        // 初始化为解密模式
        cipher.init(Cipher.DECRYPT_MODE, secretKey);
        // 将Base64编码的字符串解码成字节数组
        byte[] decodedBytes = Base64.getDecoder().decode(encryptedData);
        // 执行解密操作
        byte[] decryptedBytes = cipher.doFinal(decodedBytes);
        return new String(decryptedBytes, "UTF-8");
    }

}
