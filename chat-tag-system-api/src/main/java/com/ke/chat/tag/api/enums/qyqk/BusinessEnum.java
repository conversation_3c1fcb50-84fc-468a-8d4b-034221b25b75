package com.ke.chat.tag.api.enums.qyqk;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum BusinessEnum {
    JIAZHUANG("QYQK_JIAZHUANG", "家装"),
    JINGJI("QYQK_JINGJI", "经纪"),
    HUIJU("QYQK_HUIJU", "恵居"),
        ;

    public final String business;
    public final String businessName;

    public static BusinessEnum getByBusiness(String business) {
        for (BusinessEnum businessEnum : BusinessEnum.values()) {
            if (businessEnum.getBusiness().equals(business)) {
                return businessEnum;
            }
        }
        return null;
    }
}
