package com.ke.chat.tag.api.enums;


public enum TagRuleTypeEnum {

    TASK_TYPE_RANGE(2, "对任务类型生效"),
    BATCH_RANGE(1, "对批次生效"),
    UNDEFINED_RANGE(0, "未定义");

    public final int code;
    public final String desc;

    TagRuleTypeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static TagRuleTypeEnum valueOf(int type) {
        for (TagRuleTypeEnum typeEnum : values()) {
            if (typeEnum.code == type) {
                return typeEnum;
            }
        }
        return null;
    }
}
