package com.ke.chat.tag.api.enums.label;


public enum EvaluationTypeEnum {

    // 1:，2：，3：
    NOT_HELPFUL(3, "没有帮助"),
    INCORRECT(2, "回答有误"),
    HARMFUL(1, "信息存在危害性");


    public final int type;
    public final String desc;

    EvaluationTypeEnum(int type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public static EvaluationTypeEnum fromType(int type) {
        for (EvaluationTypeEnum typeEnum : values()) {
            if (type == typeEnum.type) {
                return typeEnum;
            }
        }
        return null;
    }

}
