package com.ke.chat.tag.api.enums.exam;

import com.ke.boot.common.exception.BusinessException;
import lombok.Getter;

import java.util.Arrays;

@Getter
public enum AiExamQuestionType {

    SINGLE_CHOICE("single_choice", "单选题"),
    MULTIPLE_CHOICE("multiple_choice", "多选题"),
    TRUE_OR_FALSE("true_or_false", "判断题");

    private String type;
    private String desc;

    AiExamQuestionType(String type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public static AiExamQuestionType getByType(String type) {
        return Arrays.stream(AiExamQuestionType.values())
            .filter(i -> i.getType().equals(type))
            .findFirst()
            .orElseThrow(() -> new BusinessException("400", "not supported exam type"));
    }
}
