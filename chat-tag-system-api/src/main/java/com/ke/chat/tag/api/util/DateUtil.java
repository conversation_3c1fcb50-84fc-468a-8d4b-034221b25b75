package com.ke.chat.tag.api.util;


import cn.hutool.core.date.DateTime;
import cn.hutool.core.util.ObjectUtil;
import lombok.extern.slf4j.Slf4j;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.Calendar;
import java.util.Date;
import java.util.Objects;
import java.util.Optional;
import java.util.Random;

@Slf4j
public class DateUtil {

    public static final String DATE_TIME_FORMAT = "yyyy-MM-dd HH:mm:ss";

    public static final String yyyyMMddHHmm = "yyyyMMddHHmm";

    public static final Random random = new Random();

    private DateUtil() {

    }

    private static final DateTimeFormatter FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    public static String formatToStr(long timestamp) {
        return ZonedDateTime.ofInstant(Instant.ofEpochMilli(timestamp), ZoneId.systemDefault())
            .format(FORMATTER);
    }

    /**
     * 将LocalDateTime 类型的对象转换为 yyyy-MM-dd HH:mm:ss 格式的String
     */
    public static String localDateTime2Str(LocalDateTime dateTime) {
        return dateTime.format(DateTimeFormatter.ofPattern(DATE_TIME_FORMAT));
    }


    /**
     * 通过时间戳格式化
     *
     * @param timeStamp 时间戳
     * @return yyyy-mm-dd hh:mm:ss
     */
    public static String formatDateByTimeStamp(Long timeStamp) {
        if (ObjectUtil.isEmpty(timeStamp)) {
            return "";
        }
        DateTime date = cn.hutool.core.date.DateUtil.date(timeStamp * 1000);
        return date.toString();
    }

    public static boolean isYesterday(long time) {
        boolean isYesterday = false;
        Date date;
        try {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            date = sdf.parse(sdf.format(new Date()));
            if (time < date.getTime() && time > (date.getTime() - 24 * 60 * 60 * 1000)) {
                isYesterday = true;
            }
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return isYesterday;
    }

    public static boolean isThisMonth(long time) {
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Calendar c = Calendar.getInstance();
        c.setTime(new Date());
        c.add(Calendar.MONTH, -1);
        Date m = c.getTime();
        return m.getTime() < time;
    }


    /**
     * 将指定的 Date 对象格式化为 ISO 8601 标准日期格式（yyyy-MM-dd）
     *
     * @param date 需要格式化的日期对象，不能为空
     * @return 符合 ISO 8601 标准的日期格式字符串
     * @throws IllegalArgumentException 如果 date 为 null
     */
    public static String formatToIsoDate(Date date) {
        if (date == null) {
            throw new IllegalArgumentException("输入日期不能为空");
        }
        return date.toInstant()
            .atZone(ZoneId.systemDefault())
            .format(DateTimeFormatter.ISO_LOCAL_DATE);
    }


    /**
     * 获取昨天日期的0点0时0分0秒
     *
     * @return 昨天的日期
     */
    public static Date getYesterday() {
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DATE, -1);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar.getTime();
    }

    /**
     * 获取n天前的日期
     *
     * @param date 日期
     * @return n天前的日期
     */
    public static Date getNDaysAgo(Date date, int n) {
        return getNDaysAfter(date, -n);
    }

    /**
     * 获取n天后的时间
     *
     * @param date 输入的日期
     * @param n    天数
     * @return n天后的日期
     */
    public static Date getNDaysAfter(Date date, int n) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.DATE, n);
        return calendar.getTime();
    }

    /**
     * 两个日期相差的天数
     *
     * @param startDate
     * @param endDate
     * @return
     */
    public static Long diffDays(Date startDate, Date endDate) {
        // 将 Date 转换为 LocalDate（注意时区问题）
        LocalDate start = startDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        LocalDate end = endDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();

        // 直接计算天数差
        return ChronoUnit.DAYS.between(start, end);
    }

    /**
     * 判断两个日期是否是同一天
     *
     * @param date1
     * @param date2
     * @return
     */
    public static boolean isSameDay(Date date1, Date date2) {
        // 将 Date 转换为 LocalDate（注意时区问题）
        LocalDate localDate1 = date1.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        LocalDate localDate2 = date2.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        return localDate1.isEqual(localDate2);
    }

    /**
     * 获取指定日期道指定日期内的随机时间
     *
     * @param date      最大日期
     * @param n         往前可以获取的天数
     * @param startTime 每天的开始时间
     * @param endTime   每天的结束时间
     * @return 随机时间
     */
    public static Date getRandomTime(Date date, int n, int startTime, int endTime) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);

        // 重置到当天零点
        cal.set(Calendar.HOUR_OF_DAY, 0);
        cal.set(Calendar.MINUTE, 0);
        cal.set(Calendar.SECOND, 0);
        cal.set(Calendar.MILLISECOND, 0);
        // 生成0到n的随机天数（包含n天）
        int daysBack = random.nextInt(n + 1);
        cal.add(Calendar.DATE, -daysBack);

        // 生成小时范围内的随机时间
        int hourSpan = endTime - startTime;
        cal.set(Calendar.HOUR_OF_DAY, startTime + random.nextInt(hourSpan));
        cal.set(Calendar.MINUTE, random.nextInt(60));
        cal.set(Calendar.SECOND, random.nextInt(60));
        cal.set(Calendar.MILLISECOND, random.nextInt(1000));

        return cal.getTime();
    }

    /**
     * 获取n周前的日期
     *
     * @param date 输入的日期
     * @param n    前面n周 （n >= 0）
     * @return 前面n周的开始日期
     */
    public static Date getNWeeksAgo(Date date, int n) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        // 设置周一为一周的第一天
        calendar.setFirstDayOfWeek(Calendar.MONDAY);
        // 调整到当周的周一
        calendar.set(Calendar.DAY_OF_WEEK, Calendar.MONDAY);
        // 将时间部分清零（00:00:00.000）
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        // 减去n周
        calendar.add(Calendar.WEEK_OF_YEAR, -n);
        return calendar.getTime();
    }

    /**
     * 获取当前周周日的时间
     *
     * @param date
     * @return
     */
    public static Date getWeekEndDay(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        // 设置周一为一周的第一天
        calendar.setFirstDayOfWeek(Calendar.MONDAY);
        // 调整到当周周日（当周最后一天）
        calendar.set(Calendar.DAY_OF_WEEK, Calendar.SUNDAY);
        // 时间部分清零
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar.getTime();
    }

    /**
     * 检查传入的时间是否在最近指定天数内
     *
     * @param time 需要检查的时间
     * @param days 天数
     * @return 如果在指定天数内返回true，否则返回false
     */
    public static boolean isInLastDays(LocalDateTime time, int days) {
        if (Objects.isNull(time)) {
            return false;
        }
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime daysAgo = now.minusDays(days);
        // 检查时间是否在daysAgo之后且在now之前
        return !time.isBefore(daysAgo) && !time.isAfter(now);
    }

    /**
     * 将 LocalDateTime 转换为自 UNIX 纪元（1970-01-01T00:00:00Z）起的毫秒数【用的是 JVM 所在机器的 系统默认时区】
     *
     * @param time 要转换的 LocalDateTime
     * @return 毫秒数
     */
    public static long toEpochMilli(LocalDateTime time) {
        if (time == null) {
            throw new IllegalArgumentException("time 不能为 null");
        }
        return time.atZone(ZoneId.systemDefault())
            .toInstant()
            .toEpochMilli();
    }
    public static LocalDateTime convertLongToLocalDateTime(Long timestamp) {
        return Optional.ofNullable(timestamp)
            .map(ts -> LocalDateTime.ofInstant(Instant.ofEpochMilli(ts), ZoneId.systemDefault()))
            .orElse(null);
    }
}
