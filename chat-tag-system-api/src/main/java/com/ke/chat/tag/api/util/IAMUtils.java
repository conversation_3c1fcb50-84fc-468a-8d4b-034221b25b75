package com.ke.chat.tag.api.util;

import com.lianjia.openiam.validator.Constants;
import org.apache.commons.lang3.StringUtils;

import javax.servlet.http.HttpServletRequest;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class IAMUtils {

    public static String extractServiceId(HttpServletRequest servletRequest) {
        Object iamPrincipal = servletRequest.getAttribute(Constants.IAM_PRINCIPAL_ATTR);
        String serviceId = extractServiceId((String) iamPrincipal);
        if (StringUtils.isBlank(serviceId)) {
            throw new RuntimeException("提取ServiceId失败:" + iamPrincipal);
        }
        return serviceId;
    }

    /**
     * 从iam标识中提取serviceID；
     *
     * @param iamPrincipal
     * @return
     */
    public static String extractServiceId(String iamPrincipal) {
        if (StringUtils.isBlank(iamPrincipal)) {
            throw new RuntimeException("iam标识为空，无法解析serviceId");
        }
        //通用模式:lj:iam::11419:role/rdpt-taigong-common-service
        String regexOtherService = "role/(.*)";
        //如果是服务自己调用，标识为如下格式 lj:iam:::bk-chat-gpt
        String regexServiceSelf = ":::(.*)";
        String serviceId = extraServiceId(regexOtherService, iamPrincipal);
        if (StringUtils.isNotBlank(serviceId)) {
            return serviceId;
        }
        return extraServiceId(regexServiceSelf, iamPrincipal);
    }

    private static String extraServiceId(String regex, String iamPrincipal) {
        //如果是服务自己调用，标识为如下格式 lj:iam:::bk-chat-gpt
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(iamPrincipal);
        if (matcher.find()) {
            return matcher.group(1);
        }
        return null;
    }
}
