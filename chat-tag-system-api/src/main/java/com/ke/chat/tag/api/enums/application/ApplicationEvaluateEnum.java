package com.ke.chat.tag.api.enums.application;

import lombok.Getter;

/**
 * <AUTHOR>
 */
@Getter
public enum ApplicationEvaluateEnum {

    DEFAULT(0, "默认"),
    LIKE(1, "点赞"),
    CANCEL_LIKE(2, "取消点赞"),
    COLLECT(3, "收藏"),
    UNCOLLECT(4, "取消收藏"),
    DISLIKE(5, "点踩"),
    CANCEL_DISLIKE(6, "取消点踩"),
    ;

    private final Integer code;
    private final String desc;

    ApplicationEvaluateEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}
