package com.ke.chat.tag.api.util;

import lombok.extern.slf4j.Slf4j;

import java.net.InetAddress;

/**
 * <AUTHOR>
 * @date 2023/3/3 11:10 上午
 **/
@Slf4j
public class IPUtil {

    private static final String DEFAULT_PORT = "8080";

    public static String getHostIp() {
        try {
            return InetAddress.getLocalHost().getHostAddress();
        } catch (Exception e) {
            LOGGER.info("getHostIp Exception: ", e);
            return "";
        }
    }

    public static String getHostIpWithDefaultPort() {
        try {
            return InetAddress.getLocalHost().getHostAddress() + ":" + DEFAULT_PORT;
        } catch (Exception e) {
            LOGGER.info("getHostIp Exception: ", e);
            return "";
        }
    }
}
