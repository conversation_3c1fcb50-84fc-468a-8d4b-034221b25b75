package com.ke.chat.tag.api.dto.application;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * 网页应用信息
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@SuperBuilder
@JsonInclude(JsonInclude.Include.NON_NULL)
public class WebApplicationInfo {

    /**
     * 图片链接
     */
    private String imgUrl;

    /**
     * 应用名称
     */
    private String appName;

    /**
     * 应用描述
     */
    private String desc;

    /**
     * 应用跳转链接
     */
    private String appUrl;

    /**
     * 应用渲染位置
     */
    private Integer position;

    /**
     * 默认：平台内置
     */
    private String source;

    /**
     * 点赞数：默认0
     */
    private Integer likes;

    /**
     * 热度：默认0
     */
    private Integer heatTemp;
}
