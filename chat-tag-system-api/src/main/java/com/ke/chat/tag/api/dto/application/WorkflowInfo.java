package com.ke.chat.tag.api.dto.application;

import java.util.List;

import com.ke.chat.tag.api.dto.application.WorkflowDTO.Workflow;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@AllArgsConstructor
@NoArgsConstructor
@SuperBuilder(toBuilder = true)
@Data
public class WorkflowInfo {

    private List<Workflow> workflows;
}
