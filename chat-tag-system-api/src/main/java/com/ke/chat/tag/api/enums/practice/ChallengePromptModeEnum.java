package com.ke.chat.tag.api.enums.practice;

import lombok.Getter;

@Getter
public enum ChallengePromptModeEnum {
    /**
     * 角色对话 - 与特定角色进行的对话
     */
    SINGLE_PROMPT(0,"单智能体prompt"),

    /**
     * 知识问答 - 基于知识的问答交互
     */
    MULTI_PROMPT(1, "多智能体prompt");

    ChallengePromptModeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    private int code;
    private String desc;

    public static String getDescByCode(int code) {
        for (ChallengePromptModeEnum mode : ChallengePromptModeEnum.values()) {
            if (mode.getCode() == code) {
                return mode.getDesc();
            }
        }
        return "";
    }
}
