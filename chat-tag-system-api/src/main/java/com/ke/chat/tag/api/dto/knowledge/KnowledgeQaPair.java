package com.ke.chat.tag.api.dto.knowledge;

import com.ke.chat.tag.api.dto.home.base.ResignUrl;
import com.ke.chat.tag.api.validator.annotation.StringListElementLength;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.Size;
import java.util.List;
import java.util.function.Function;
import java.util.stream.Collectors;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class KnowledgeQaPair implements ResignUrl {

    @Length(max = 4096, message = "question不能超过4096个字符")
    private String question;

    @Length(max = 4096, message = "answer不能超过4096个字符")
    private String answer;

    @Size(max = 10, message = "imgList不能超过10个")
    private List<String> imgList;

    @Size(max = 20, message = "cityList不能超过20个")
    private List<String> cityList;

    @Size(max = 20, message = "businessTags不能超过20个")
    @StringListElementLength(maxLength = 32, message = "每个标签最多不能超过32个字符")
    private List<String> businessTags;

    private List<String> similarQuestions;

    @Override
    public void resign(Function<String, String> resignFunc) {
        if (CollectionUtils.isEmpty(imgList)) {
            return;
        }
        imgList = imgList.stream()
            .map(i -> resignFunc.apply(i))
            .collect(Collectors.toList());
    }
}
