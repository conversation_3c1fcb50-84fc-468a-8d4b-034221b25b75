package com.ke.chat.tag.api.enums.partner;

import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * function: 伙伴类型枚举
 *
 * <AUTHOR>
 */
@Getter
public enum PartnerTypeEnum {

    MANAGE("manage", "管理助手"),

    QA("qa", "问答助手")

    ;

    public static final List<String> ALL_TYPE_CODES =
        Arrays.stream(PartnerTypeEnum.values())
            .map(PartnerTypeEnum::getCode)
            .collect(Collectors.toList());


    private final String code;

    private final String name;

    PartnerTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static String getNameByCode(String code) {
        for (PartnerTypeEnum partnerTypeEnum : PartnerTypeEnum.values()) {
            if (partnerTypeEnum.code.equals(code)) {
                return partnerTypeEnum.name;
            }
        }
        return null;
    }


}
