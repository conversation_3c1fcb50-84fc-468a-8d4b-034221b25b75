package com.ke.chat.tag.api.enums.practice;

import lombok.Getter;

@Getter
public enum SearchDataCategoryEnum {

    TAG("tag", "标签"),
    CHALLENGE("challenge", "挑战"),
    CHALLENGE_RECORD("challenge_record", "挑战记录");

    SearchDataCategoryEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    private final String code;

    private final String description;

}
