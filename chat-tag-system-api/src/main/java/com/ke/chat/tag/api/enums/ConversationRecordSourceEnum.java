package com.ke.chat.tag.api.enums;

import lombok.Getter;

/**
 * 会话来源枚举
 *
 * <AUTHOR>
 */
@Getter
public enum ConversationRecordSourceEnum {
    OPEN_KE_GPT((byte) 1, "OpenKeGPT体验平台"),
    BELLA((byte) 2, "bella平台"),

    API((byte) 3, "API接口"),

    GROUP_BOT((byte) 4, "群聊机器人"),

    VIRTUAL_BOT((byte) 5, "企微虚拟号");


    public final Byte code;
    public final String desc;

    ConversationRecordSourceEnum(Byte code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}
