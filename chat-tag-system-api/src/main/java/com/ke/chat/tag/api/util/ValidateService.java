package com.ke.chat.tag.api.util;

import com.alibaba.fastjson.JSON;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.validation.BeanPropertyBindingResult;
import org.springframework.validation.Errors;
import org.springframework.validation.Validator;

import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/4/25 10:40 上午
 **/
@Service
public class ValidateService {
    @Autowired
    private Validator validator;

    /**
     * 返回验证错误信息
     *
     * @param obj 验证对象
     * @return
     */
    public String validate(Object obj) {
        Errors errors = new BeanPropertyBindingResult(obj, obj.getClass().getCanonicalName());
        validator.validate(obj, errors);
        if (CollectionUtils.isEmpty(errors.getFieldErrors())) {
            return null;
        }
        return errors.getFieldErrors().stream().map(
            x -> String.join(":", x.getField(),
                JSON.toJSONString(x.getRejectedValue()),
                x.getDefaultMessage())
        ).collect(Collectors.joining());
    }
}
