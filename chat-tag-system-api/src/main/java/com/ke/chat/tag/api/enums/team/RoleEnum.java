package com.ke.chat.tag.api.enums.team;

import com.google.common.collect.ImmutableList;
import lombok.Getter;

import java.util.List;

/**
 * function:
 *
 * <AUTHOR>
 */
@Getter
public enum RoleEnum {

    OWNER("owner", "所有者"),

    ADMIN("admin", "管理员"),

    MEMBER("member", "成员");

    RoleEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    private final String code;

    private final String desc;

    public static final List<String> ownerOrAdmin = ImmutableList.of(OWNER.getCode(), ADMIN.getCode());
}
