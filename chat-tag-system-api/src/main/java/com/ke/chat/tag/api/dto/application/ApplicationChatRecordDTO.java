package com.ke.chat.tag.api.dto.application;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.ke.chat.tag.api.dto.evaluation.ChatEvaluation;
import com.ke.chat.tag.api.dto.knowledge.KnowledgeItemInfo;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ApplicationChatRecordDTO {
    private String messageId;
    private String question;
    private String answer;
    private List<RecallResult> knowledgeList;
    private Integer source;
    private Integer isThumbsUp;
    private String reason;
    private String time;
    //是否已添加到知识库:已回流，未回流
    private String status;
    private String chunkId;
    private String groupId;
    //	标识应用
    private Long applicationId;
    private String applicationName;
    private List<String> tagList;
    /**
     * 对话人ID
     */
    private String userId;
    /**
     * 对话人姓名
     */
    private String userName;

    private Date createTime;
    private Long id;

    private Long questionId;
    /**
     * 更新人ID
     */
    private String muid;

    private String updaterName;
    private ChatEvaluation evaluations;
    private String thumbsUpReasons;

    private KnowledgeItemInfo knowledgeItemInfo;
    /**
     * 是智能体拥有者（true表示是，false表示不是）
     */
    @JsonProperty("isOwnerUser")
    private boolean isOwnerUser;

    /**
     * 授权空间标识（true表示是授权过来的智能体，false表示不是授权过来的智能体）
     */
    private boolean authFlag;
}
