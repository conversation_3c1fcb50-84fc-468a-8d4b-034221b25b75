package com.ke.chat.tag.api.dto.application.index;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @Author: gaopan034
 * @Date: 2024/11/1
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ApplicationIndexSearchDTO {

    private Long innerId;

    private Long outerId;

    private List<Long> innerIds;

    private List<Long> outerIds;

    private Integer pageNum;

    private Integer pageSize;

    private String name;

    private String ownerUserId;

    private String visibility;

    private List<String> authUserCodes;

    private List<String> orgTags;

    private List<String> functionTags;

    private List<String> universalTags;

    private List<String> cityTags;
    /**
     * 可复制智能体标签
     */
    private List<String> authTags;
    /**
     * 智能体模式
     */
    private String mode;
    private List<Long> filterInnerAppIds;

    //目前支持按照时间或热度排序, 默认热度
    private String sort = "heat";
}
