package com.ke.chat.tag.api.enums.answer;

import com.google.common.collect.ImmutableList;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;

/**
 * function: 对话内容评论标识枚举值【1 点赞 ；-1 点踩 ；2取消点赞；3取消点踩】
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum AnswerFeedbackEnum {

    LIKE(1, "点赞"),

    DISLIKE(-1, "点踩"),

    CANCEL_LIKE(2, "取消点赞"),

    CANCEL_DISLIKE(3, "取消点踩");

    private final Integer code;

    private final String desc;

    public static final List<Integer> CANCEL_CODES = ImmutableList.of(CANCEL_LIKE.getCode(), CANCEL_DISLIKE.getCode());

    public static boolean isValidCode(Integer code) {
        return Arrays.stream(values()).anyMatch(enumValue -> enumValue.getCode().equals(code));
    }

}
