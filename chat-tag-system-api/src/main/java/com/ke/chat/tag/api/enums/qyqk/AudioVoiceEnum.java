package com.ke.chat.tag.api.enums.qyqk;

public enum AudioVoiceEnum {

    <PERSON><PERSON>("<PERSON><PERSON>", 2),
    <PERSON>("Echo", 1),
    <PERSON><PERSON>("Fable", 2),
    <PERSON><PERSON>("Onyx", 1),
    <PERSON>("<PERSON>", 2),
    <PERSON><PERSON>("<PERSON><PERSON>", 2),
    ;

    public final String voice;

    /**
     * 0-未知
     * 1-男
     * 2-女
     */
    public final int sex;

    AudioVoiceEnum(String voice, int sex) {
        this.voice = voice;
        this.sex = sex;
    }

}
