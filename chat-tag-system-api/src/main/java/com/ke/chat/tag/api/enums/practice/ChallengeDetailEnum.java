package com.ke.chat.tag.api.enums.practice;

import lombok.Getter;

import java.util.Objects;

@Getter
public enum ChallengeDetailEnum {


    // 挑战初始设置
    DIFFICULTY("difficulty", "难度", ChallengeDetailContentTypeEnum.TEXT, ChallengeStageEnum.INITIAL_SETUP, true),
    DURATION("duration", "参考时长", ChallengeDetailContentTypeEnum.TEXT, ChallengeStageEnum.INITIAL_SETUP, true),
    TTS_AUDIO_MANAGER("ttsAudioManager", "音色配置", ChallengeDetailContentTypeEnum.JSON, ChallengeStageEnum.INITIAL_SETUP, true),
    PERSONALITY_LINES("personalityLines", "个性化语句文本", ChallengeDetailContentTypeEnum.TEXT, ChallengeStageEnum.INITIAL_SETUP,false),
    BACKGROUND_IMAGE("backgroundImage", "挑战背景图", ChallengeDetailContentTypeEnum.IMAGE, ChallengeStageEnum.INITIAL_SETUP, false),
    SCRIPT_DESCRIPTION("scriptDescription", "剧本描述", ChallengeDetailContentTypeEnum.JSON, ChallengeStageEnum.INITIAL_SETUP, true),
    CHALLENGE_WAY("challengeWay", "挑战方式", ChallengeDetailContentTypeEnum.TEXT, ChallengeStageEnum.INITIAL_SETUP, true),

    // 挑战过程控制
    OPENING_REMARKS("openingRemarks", "开场白", ChallengeDetailContentTypeEnum.TEXT, ChallengeStageEnum.PROCESS_CONTROL,false),
    OPENING_REMARKS_AUDIO("openingRemarksAudio", "开场白音频", ChallengeDetailContentTypeEnum.AUDIO, ChallengeStageEnum.PROCESS_CONTROL,true),
    PROMPT_MANAGER("promptManager", "提示语配置管理", ChallengeDetailContentTypeEnum.JSON, ChallengeStageEnum.PROCESS_CONTROL,true),
    SCORE_MANAGER("scoreManager", "评分弹窗配置", ChallengeDetailContentTypeEnum.JSON, ChallengeStageEnum.PROCESS_CONTROL,false),

    //挑战结果评价
    EVALUATION_MANAGER("evaluationManager", "评价方案配置", ChallengeDetailContentTypeEnum.JSON, ChallengeStageEnum.RESULT_EVALUATION,true),
    SHOW_DURATION("showDuration", "展示时长", ChallengeDetailContentTypeEnum.TEXT, ChallengeStageEnum.RESULT_EVALUATION,false),
    RESISTANCE_PROBLEM("resistanceProblem", "抗拒问题配置", ChallengeDetailContentTypeEnum.JSON, ChallengeStageEnum.RESULT_EVALUATION,false),
    FEEDBACK_MANAGER("feedbackManager", "反馈弹窗配置", ChallengeDetailContentTypeEnum.JSON, ChallengeStageEnum.RESULT_EVALUATION, false),
    ;
    private String property;
    private String propertyDesc;
    private ChallengeDetailContentTypeEnum type;
    private ChallengeStageEnum challengeStage;
    private Boolean need;

    ChallengeDetailEnum(String property, String propertyDesc, ChallengeDetailContentTypeEnum type, ChallengeStageEnum challengeStage, Boolean need) {
        this.property = property;
        this.propertyDesc = propertyDesc;
        this.type = type;
        this.challengeStage = challengeStage;
        this.need = need;
    }

    public static ChallengeDetailEnum getByProperty(String property) {
        for (ChallengeDetailEnum value : ChallengeDetailEnum.values()) {
            if (Objects.equals(value.getProperty(), property)) {
                return value;
            }
        }
        return null;
    }
}
