package com.ke.chat.tag.api.enums.application;

import com.ke.boot.common.exception.BusinessException;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

@Getter
@AllArgsConstructor
public enum ConversationSceneEnum {

    DEFAULT("default", "默认sse对话模式：从应用关联知识检索，关联应用最新轮上下文"),

    ASSISTANT("assistant", "assistant对话"),

    TEMP("temp", "临时会话：会话记录不保存"),

    FAVORITE("favorite", "对话被用户收藏"),

    DROPPED("dropped", "会话被用户清理");

    private String scene;

    private String desc;

    public static String getConvScene(String convScene) {
        if (convScene == null) {
            return DEFAULT.getScene();
        }
        if (Arrays.stream(ConversationSceneEnum.values()).noneMatch(i -> i.getScene().equals(convScene))) {
            // 不支持的场景类型
            throw new BusinessException("400", String.format("不支持会话场景%s，请重新检查", convScene));
        }
        return convScene;
    }

    public static ConversationSceneEnum getSceneByConv(String convScene) {
        return Arrays.stream(ConversationSceneEnum.values())
            .filter(i -> i.getScene().equals(convScene))
            .findFirst().orElse(DEFAULT);
    }
}
