package com.ke.chat.tag.api.enums;

import com.google.common.collect.Lists;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/7/10 11:44 上午
 **/
public enum KnowledgeDirEnum {

    DECORATION("家装", "整装"),
    HOUSING("房产", "经纪"),
    DWELL("惠居", "惠居"),
    OTHER("其他", "其他"),
    QW("企微", "企微"),
    CHAT("对话", "对话"),
    // Bella上传文件不在区分dir，置为默认
    DEFAULT("默认", "默认");

    public final String code;
    // 外部展示
    public final String display;

    KnowledgeDirEnum(String code, String display) {
        this.code = code;
        this.display = display;
    }

    public String getCode() {
        return this.code;
    }

    public String getDisplay() {
        return this.display;
    }

    /**
     * 获取同一类型的目录列表
     *
     * @param dir 目录
     * @return
     */
    public static List<String> getDirList(String dir) {
        return Arrays.stream(KnowledgeDirEnum.values())
            .filter(i -> i.getCode().equals(dir) || i.getDisplay().equals(dir))
            .map(i -> Lists.newArrayList(i.getCode(), i.getDisplay()))
            .findFirst()
            .orElse(new ArrayList<>());
    }
}
