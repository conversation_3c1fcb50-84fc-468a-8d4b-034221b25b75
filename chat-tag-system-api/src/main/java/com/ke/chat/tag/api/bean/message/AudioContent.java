package com.ke.chat.tag.api.bean.message;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@Data
@AllArgsConstructor
@NoArgsConstructor
@SuperBuilder
public class AudioContent extends BaseContent {

    private Object audio;


    /**
     * 内容
     */
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class Audio {
        private String transcript;
        private String audio;
        private Integer interrupt;
    }

}
