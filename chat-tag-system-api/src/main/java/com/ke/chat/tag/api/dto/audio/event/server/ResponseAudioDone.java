package com.ke.chat.tag.api.dto.audio.event.server;

import com.ke.chat.tag.api.dto.audio.event.BaseEvent;
import lombok.Data;
import lombok.Getter;

import static com.ke.chat.tag.api.enums.audio.EventEnum.RESPONSE_AUDIO_DONE;

/**
 * 当模型生成音频数据结束时，或响应中断，取消时响应该事件
 */
@Data
public class ResponseAudioDone extends BaseEvent {

    public final String type = RESPONSE_AUDIO_DONE.type;

    private String responseId;

    private String itemId;

    private Integer contentIndex;
}
