package com.ke.chat.tag.api.enums.practice;

import lombok.Getter;

@Getter
public enum ChallengeDetailContentTypeEnum {
    /**
     * 文字
     */
    TEXT("text", "文字"),
    /**
     * 图片
     */
    IMAGE("image", "图片"),
    /**
     * 视频
     */
    VIDEO("video", "视频"),
    /**
     * 音频
     */
    AUDIO("audio", "音频"),
    /**
     * 其他
     */
    JSON("json", "结构体");

    private String type;
    private String desc;

    ChallengeDetailContentTypeEnum(String type, String desc) {
        this.type = type;
        this.desc = desc;
    }
}
