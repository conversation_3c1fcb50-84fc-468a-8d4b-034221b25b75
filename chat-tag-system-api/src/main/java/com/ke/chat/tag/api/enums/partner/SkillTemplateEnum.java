package com.ke.chat.tag.api.enums.partner;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/01/03
 */
public enum SkillTemplateEnum {
    /**
     * 技能模板枚举
     */
    CRON_WECHAT_MESSAGE("cron_wechat_message", "定时企微消息"),
    WECHAT_MESSAGE_COLLATES("wechat_message_collates", "企微群消息整理");


    public final String code;
    public final String desc;

    private static Map<String, SkillTemplateEnum> map = new HashMap<>();

    SkillTemplateEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }


    static {
        for (SkillTemplateEnum skillTemplateEnum : SkillTemplateEnum.values()) {
            map.put(skillTemplateEnum.code, skillTemplateEnum);
        }
    }

    public static SkillTemplateEnum getByCode(String code) {
        return map.get(code);
    }
}
