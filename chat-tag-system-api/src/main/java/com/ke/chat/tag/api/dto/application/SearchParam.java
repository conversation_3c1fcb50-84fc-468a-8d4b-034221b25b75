package com.ke.chat.tag.api.dto.application;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class SearchParam {

    /**
     * 相似度设置
     */
    @Builder.Default
    private Float similarity = 0.8f;

    /**
     * 检索数量设置
     */
    @Builder.Default
    private Integer num = 3;

    /**
     * 空搜索回复
     */
    @Builder.Default
    private String emptySearchReply = "";
}
