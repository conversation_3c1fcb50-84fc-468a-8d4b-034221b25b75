package com.ke.chat.tag.api.dto.application;

import com.ke.chat.tag.api.dto.card.CardBoardInfoResponseDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.List;
import java.util.Map;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class BaseToolInfoRespDTO {
    private Map<String, GetToolInfo> toolInfo;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class GetToolInfo {
        private Button button;
        private CardInfo cardInfo;

        public GetToolInfo(Button button) {
            this.button = button;
        }
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Button {
        private boolean display = false; // 更改为 display 以匹配 JSON 键名
        private String copyWriting;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Accessors(chain = true)
    public static class CardInfo {
        private boolean display = false;
        private List<CardBoardInfoResponseDTO> infoList;

    }

}
