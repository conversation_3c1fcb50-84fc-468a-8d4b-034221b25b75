package com.ke.chat.tag.api.dto.application;

import com.ke.chat.tag.api.dto.home.base.ResignUrl;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.function.Function;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class BannerAdInfo implements ResignUrl {

    private String url;

    private String imageUrl;

    @Override
    public void resign(Function<String, String> resignFunc) {
        imageUrl = resignFunc.apply(imageUrl);
    }
}
