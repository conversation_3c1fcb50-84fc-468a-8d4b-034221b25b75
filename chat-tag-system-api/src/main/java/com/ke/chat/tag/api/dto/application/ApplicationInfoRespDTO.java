package com.ke.chat.tag.api.dto.application;

import com.ke.chat.tag.api.response.BasePageRequest;
import com.ke.chat.tag.api.response.BasePageResponse;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ApplicationInfoRespDTO extends BasePageResponse {

    private List<ApplicationInfo> applications;

    private Boolean hasMore;

    public ApplicationInfoRespDTO(BasePageRequest request, Integer total) {
        super(request, total);
    }
}
