package com.ke.chat.tag.api.enums.application;

import lombok.Getter;

/**
 * <AUTHOR>
 */

@Getter
public enum ApplicationSourceEnum {

    PLATFORM("platform", "平台内置"),
    USER("user", "用户创建");

    private final String name;
    private final String desc;

    ApplicationSourceEnum(String name, String desc) {
        this.name = name;
        this.desc = desc;
    }

    public static ApplicationSourceEnum getSourceEnumByName(String name) {
        for (ApplicationSourceEnum value : ApplicationSourceEnum.values()) {
            if (value.getName().equals(name)) {
                return value;
            }
        }
        return null;
    }
}
