package com.ke.chat.tag.api.enums.lumirole;

import java.util.HashMap;
import java.util.Map;

/**
 * 实时交互事件枚举
 *
 * <AUTHOR>
 * @date 2025/02/10
 */
public enum RealtimeInteractionEventEnum {

    INPUT_AUDIO_BUFFER_PRE_TEN_PACKAGE_COMMIT("input_audio_buffer.pre_ten_package_commit", "自定义-client-写入语音结束前十包"),
    INPUT_AUDIO_BUFFER_COMMIT("input_audio_buffer.commit", "client-写入语音结束"),
    INPUT_AUDIO_BUFFER_APPEND_FIRST("input_audio_buffer.append.first", "自定义-client-服务器开始返回数据"),
    DEVICE_READY_PLAY("device.ready_play", "自定义-client-准备播放的时间"),
    DEVICE_PLAY("device.play", "自定义-client-声卡发声"),

    INPUT_AUDIO_BUFFER_COMMITTED("input_audio_buffer.committed", "server-接收到写入语音结束消息"),
    INPUT_AUDIO_TRANSCRIPT_DONE("input_audio_transcript.done", "server-ASR结束"),
    CHAT_PREPARE("chat.prepare", "准备Chat"),
    CHAT_BEGIN("chat.begin", "开始Chat"),
    CHAT_FIRST_SENTENCE("chat.first_sentence", "Chat第一句话"),
    RESPONSE_AUDIO_TTS_CONNECT("response.audio.tts.connect", "自定义-server-tts建立链接"),
    RESPONSE_AUDIO_DELTA_FIRST("response.audio.delta.first", "自定义-server-开始返回数据");


    private final String eventName;
    private final String description;

    private static final Map<String, RealtimeInteractionEventEnum> eventMap = new HashMap<>();

    static {
        for (RealtimeInteractionEventEnum value : RealtimeInteractionEventEnum.values()) {
            eventMap.put(value.eventName, value);
        }
    }


    RealtimeInteractionEventEnum(String eventName, String description) {
        this.eventName = eventName;
        this.description = description;
    }

    public String getEventName() {
        return eventName;
    }

    public String getDescription() {
        return description;
    }

    public static RealtimeInteractionEventEnum of(String eventName) {
        RealtimeInteractionEventEnum realtimeInteractionEventEnum = eventMap.get(eventName);
        if (realtimeInteractionEventEnum == null) {
            throw new IllegalArgumentException("unknown eventName: " + eventName);
        }
        return realtimeInteractionEventEnum;
    }

    public static RealtimeInteractionEventEnum ofNotThrowable(String eventName) {
        return eventMap.get(eventName);
    }
}
