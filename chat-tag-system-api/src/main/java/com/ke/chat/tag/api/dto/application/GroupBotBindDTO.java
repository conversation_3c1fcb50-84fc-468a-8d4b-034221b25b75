package com.ke.chat.tag.api.dto.application;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class GroupBotBindDTO {

    @NotNull
    private String token;

    @NotNull
    private String encodingAseKey;

    @NotNull
    private String webhook;

    @NotNull
    private Long applicationId;

    @NotNull
    private String botName;

    private String receiveUrl;
}
