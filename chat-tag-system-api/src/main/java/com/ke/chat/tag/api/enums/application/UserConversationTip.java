package com.ke.chat.tag.api.enums.application;

import com.ke.chat.tag.api.util.DateUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.Objects;

/**
 * 用户对话类型tip
 */
@AllArgsConstructor
@Getter
public enum UserConversationTip {

    TODAY("today", "今天"),

    YESTERDAY("yesterday", "昨天"),
    MONTHLY("monthly", "近30天"),
    FAVORITE("favorite", "收藏"),

    HISTORY("history", "历史");

    private String type;

    private String desc;

    public static UserConversationTip getTipByTimeAndType(String type, LocalDateTime time) {
        if (FAVORITE.getType().equals(type)) {
            return FAVORITE;
        }
        // 如果time为空，返回HISTORY
        if (Objects.isNull(time)) {
            return HISTORY;
        }
        LocalDateTime startTime = LocalDate.now().atTime(0, 0, 0);
        LocalDateTime endTime = LocalDate.now().atTime(23, 59, 59);
        if (time.isAfter(startTime) && time.isBefore(endTime)) {
            return TODAY;
        }
        if (DateUtil.isYesterday(time.toInstant(ZoneOffset.of("+8")).toEpochMilli())) {
            return YESTERDAY;
        }
        // 检查传入的时间是否是近30天
        if (DateUtil.isInLastDays(time, 30)) {
            return MONTHLY;
        }
        // 所有其他过去的时间
        return HISTORY;
    }

    public static Integer displayLevel(String type) {
        if (TODAY.getType().equals(type)) {
            return 1;
        } else if (FAVORITE.getType().equals(type)) {
            return 2;
        } else {
            return 3;
        }
    }
}
