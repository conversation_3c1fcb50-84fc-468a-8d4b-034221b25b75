package com.ke.chat.tag.api.bean.message;

import com.amazonaws.util.json.Jackson;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.ke.chat.tag.api.bean.assistant.File;
import com.ke.chat.tag.api.bean.assistant.OpenAIContent;
import com.ke.chat.tag.api.enums.application.BellaContentEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import javax.validation.constraints.AssertTrue;
import java.util.Collections;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@SuperBuilder
public class BellaContent extends BaseContent {


    /**
     * 内容：可以为string或Text结构体
     */
    private Object text;

    /**
     * 推理耗时，只有type为thinking时才有值
     */
    private Float thinkingElapsedTime;

    private File file;

    /**
     * 图片
     */
    @JsonProperty("image_file")
    private OpenAIContent.ImageFile imageFile;

    @JsonProperty("image")
    private ImageUrl image;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class ImageUrl {
        //TODO @雨函 id是不是可以基于url自动生成？这样就能和openai的imageUrl一致了
        private String id;

        private String url;

        private String detail;
    }

    // 不要删除这个无引用方法，spring会找到@AssertTrue修饰的方法进行校验
    @JsonIgnore
    @AssertTrue(message = "type为text类型时，text提问内容不可为空")
    public boolean isTextValid() {
        if (BellaContentEnum.TEXT.getType().equals(super.getType())) {
            return this.getText() != null;
        }
        return true;
    }

    public OpenAIContent.Text text() {
        return Jackson.fromJsonString(Jackson.toJsonString(text), OpenAIContent.Text.class);
    }

    public static BellaContent buildFromImageUrl(String imageUrl) {
        ImageUrl image = new ImageUrl();
        image.setUrl(imageUrl);
        return BellaContent.builder().type(BellaContentEnum.IMAGE.getType()).image(image).build();
    }


    public static BellaContent buildFromText(String query) {
        OpenAIContent.Text userText = new OpenAIContent.Text();
        userText.setValue(query);
        return BellaContent.builder().type(BellaContentEnum.TEXT.getType()).text(userText).build();
    }

    public static List<BellaContent> buildSingletonImage(String imageId, String imageUrl) {
        return Collections.singletonList(BellaContent.builder()
            .type(BellaContentEnum.IMAGE.getType())
            .image(BellaContent.ImageUrl.builder().id(imageId).url(imageUrl).build()).build());
    }


    public static BellaContent buildFromReasoning(String reasoning, Float thinkingElapsedTime) {
        return BellaContent.builder().type(BellaContentEnum.REASONING.getType())
            .text(OpenAIContent.Text.builder()
                .value(reasoning)
                .build())
            .thinkingElapsedTime(thinkingElapsedTime).build();
    }
}
