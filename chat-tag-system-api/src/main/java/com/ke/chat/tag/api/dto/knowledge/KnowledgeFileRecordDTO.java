package com.ke.chat.tag.api.dto.knowledge;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/11/21
 */
@Data
public class KnowledgeFileRecordDTO {
    /**
     * ID
     */
    private Long id;

    /**
     * 文件唯一ID
     */
    private String fileId;

    /**
     * 文件路径, 包含文件名
     */
    private String filePath;

    /**
     * 文件目录
     */
    private String dir;

    /**
     * 文件名
     */
    private String fileName;

    /**
     * 可见性
     * {@link com.ke.chat.tag.api.enums.KnowledgeFileVisibilityEnum}
     */
    private String visibility;

    /**
     * 结构
     * {@link com.ke.chat.tag.api.enums.KnowledgeStructureEnum}
     */
    private String structure;

    /**
     * 创建人系统号
     */
    private String creatorUcid;

    /**
     * 更新人系统号
     */
    private String updaterUcid;

    /**
     * 文件状态 {@link com.ke.chat.tag.api.enums.KnowledgeFileStatusEnum}
     */
    private String status;

    private LocalDateTime createTime;

    private LocalDateTime updateTime;

    /**
     * 文件敏感等级
     */
    private Integer sensitiveLevel;

    /**
     * 文件是否更新至腾讯向量库
     */
    private Boolean upsertTencent;

    /**
     * 文件适用城市
     */
    private List<String> cityList;

    /**
     * 业务标签
     */
    private List<String> businessTags;

    /**
     * 业务类型
     */
    private String businessType;

    /**
     * 文件类型
     */
    private String fileType;

    /**
     * 所属空间编码
     */
    private String spaceCode;
}
