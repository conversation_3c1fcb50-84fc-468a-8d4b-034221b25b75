package com.ke.chat.tag.api.enums.application;

import lombok.Getter;

/**
 * <AUTHOR>
 */
@Getter
public enum ApplicationStatisticsDimensionalityEnum {
    UV("uv", "用户量"),
    LIKES("likes", "点赞数"),
    INTERACTION("interaction", "互动量");

    private final String name;
    private final String desc;

    ApplicationStatisticsDimensionalityEnum(String name, String desc) {
        this.name = name;
        this.desc = desc;
    }
}
