package com.ke.chat.tag.api.dto.application;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 应用数据统计请求实体
 *
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ApplicationStatisticsReqDTO {

    /**
     * 应用id
     */
    @NotEmpty(message = "applicationIds must not be empty")
    private List<Long> applicationIds;

    /**
     * 统计维度
     * 一期前端只控制统计维度
     * 后端写死往前查询3个月内的数据
     */
    @NotNull(message = "timeDimensionality must not be blank")
    private String timeDimensionality;
}
