package com.ke.chat.tag.api.dto.application;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class BaseApplicationReqUpdateDTO extends BaseApplicationReqCreateDTO {

    /**
     * 应用id
     */
    @NotNull(message = "applicationId must not be blank")
    private Long applicationId;

}
