package com.ke.chat.tag.api.dto.application;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ApplicationBotBindDTO {

    /**
     * 应用id
     */
    private Long applicationId;

    /**
     * 虚拟号id
     */
    @NotEmpty
    private String botId;

    /**
     * 虚拟号系统号
     */
    @NotEmpty
    private String botUserCode;

    /**
     * 虚拟号昵称
     */
    @NotEmpty
    private String botName;
}
