package com.ke.chat.tag.api.enums;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @data 2023/3/31 17:11
 * @description 户型图类型
 */
public enum ImageTypeEnum {

    STANDARD(101500000003L, "标准图"),
    COMMERCIAL_RESIDENTIAL(101500000009L, "商住两用标准图"),
    COMMERCIAL_ESTATE(101500000012L, "商业地产标准图"),
    NONSTANDARD(101500000006L, "非标图"),
    MAPPING(101500000001L, "测绘图"),
    SIMPLIFIED_DIAGRAM(101500000010L, "简版户型图"),
    VECTOR_GRAPH(101500000002L, "矢量图");

    private Long imageType;
    private String message;

    ImageTypeEnum(Long imageType, String message) {
        this.imageType = imageType;
        this.message = message;
    }

    public Long getImageType() {
        return imageType;
    }

    public void setImageType(Long imageType) {
        this.imageType = imageType;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public static List<Long> getImageTypes() {
        ImageTypeEnum[] enums = ImageTypeEnum.values();
        List<Long> res = new ArrayList<>();
        for (ImageTypeEnum tempEnum : enums) {
            res.add(tempEnum.getImageType());
        }
        return res;
    }
}
