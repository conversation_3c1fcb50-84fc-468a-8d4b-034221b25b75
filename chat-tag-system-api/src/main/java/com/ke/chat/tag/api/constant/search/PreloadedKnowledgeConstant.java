package com.ke.chat.tag.api.constant.search;

/**
 * 预加载知识常量 - 返回的错误码
 *
 * <AUTHOR>
 * @date 2024/11/13
 */
public class PreloadedKnowledgeConstant {

    public static final String ERROR_500_CODE = "500";

    public static final String ERROR_500_MSG = "服务内部异常";

    public static final String PARAM_ERROR_ERROR_CODE = "10002";

    public static final String PARAM_ERROR_ERROR_MSG = "参数错误 %s";

    public static final String PRELOADED_SAVE_LOCK_PREFIX = "preloaded_save_lock_";

    public static final String PRELOADED_BELLA_APPLY_LOCK_PREFIX = "preloaded_bella_apply_lock_";

    public static final String PRELOADED_SAVE_REQUEST_PREFIX = "preloaded_save_request_";

    private PreloadedKnowledgeConstant() {
        throw new IllegalStateException("Utility class");
    }
}
