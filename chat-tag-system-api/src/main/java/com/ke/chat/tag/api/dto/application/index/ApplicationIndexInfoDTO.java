package com.ke.chat.tag.api.dto.application.index;

import com.ke.train.easy.search.boot.starter.core.BaseIndex;
import com.ke.train.easy.search.boot.starter.core.Index;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.List;


/**
 * function:
 *
 * <AUTHOR>
 * @see <a href="https://wiki.lianjia.com/display/RGZNJSZX/DATA+API">向索引中推送数据</a>
 */
@Data
@Builder
@Accessors(chain = true)
@Index("applicationIndex")
@AllArgsConstructor
@NoArgsConstructor
public class ApplicationIndexInfoDTO extends BaseIndex {

    /**
     * 智能体内部id
     */
    private Long innerId;

    /**
     * 智能体外部id
     */
    private Long outerId;

    /**
     * 智能体名称
     */
    private String name;

    /**
     * 智能体描述
     */
    private String description;

    /**
     * 智能体创建人ucid
     */
    private String ownerUserId;

    /**
     * 可见性 public:公开；private：私有
     *
     * @see com.ke.chat.tag.api.enums.application.VisibilityEnum
     */
    private String visibility;

    /**
     * 是否上架 1:是；0否
     */
    private Integer launch;

    /**
     * 是否生效 1: 生效；0: 失效
     */
    private Integer status;

    /**
     * 智能体空间信息
     */
    private String spaceCode;

    /**
     * 事业线标签
     */
    private List<String> orgTags;

    /**
     * 职能标签
     */
    private List<String> functionTags;

    /**
     * 通用标签
     */
    private List<String> universalTags;

    /**
     * 城市标签
     */
    private List<String> cityTags;
    /**
     * 可复制智能体标签
     */
    private List<String> authTags;
    /**
     * 智能体复制次数
     */
    private Long copyCount;
    /**
     * 智能体模式
     */
    private String mode;
    /**
     * 创建时间
     */
    private Long createTime;

    /**
     * 推荐问题 限定每个问题30个字
     */
    private List<String> exampleQuestions;

    /**
     * 热度
     */
    private Long heat;

}
