package com.ke.chat.tag.api.enums;

import lombok.Getter;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2023/7/7 5:07 下午
 **/
@Getter
public enum KnowledgeStructureEnum {

    UN_STRUCTURE("un_structure"),

    CSV_QA("csv_qa"),

    JSON_TEXT("json_text"),

    JSONL("jsonl"),

    HTML("html");

    public final String code;

    private static Map<String, KnowledgeStructureEnum> map = new HashMap<>();

    static {
        for (KnowledgeStructureEnum sourceEnum : KnowledgeStructureEnum.values()) {
            map.put(sourceEnum.code, sourceEnum);
        }
    }

    KnowledgeStructureEnum(String code) {
        this.code = code;
    }

    public static Optional<KnowledgeStructureEnum> getByCode(String code) {
        return Optional.ofNullable(map.get(code));
    }
}
