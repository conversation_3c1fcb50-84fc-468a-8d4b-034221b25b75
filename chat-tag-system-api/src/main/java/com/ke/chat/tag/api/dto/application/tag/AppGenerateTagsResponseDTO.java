package com.ke.chat.tag.api.dto.application.tag;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AppGenerateTagsResponseDTO {

    private List<GenerateTag> tags;

    private List<GenerateTag> orgTags;

    private List<GenerateTag> functionTags;

    private List<GenerateTag> universalTags;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class GenerateTag {
        private String tag;
        private String name;
        private String tagGroup;
        private String reason;
        private int score;
    }
}
