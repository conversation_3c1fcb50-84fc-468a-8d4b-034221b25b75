package com.ke.chat.tag.api.enums.trace;

import java.util.HashMap;
import java.util.Map;

/**
 * 基本事件模块枚举
 *
 * <AUTHOR>
 * @date 2025/02/10
 */
public enum BasicEventModuleEnum {

    LUMIROLE("LUMIROLE");

    private final String moduleName;

    private static final Map<String, BasicEventModuleEnum> moduletMap = new HashMap<>();

    static {
        for (BasicEventModuleEnum value : BasicEventModuleEnum.values()) {
            moduletMap.put(value.moduleName, value);
        }
    }

    BasicEventModuleEnum(String moduleName) {
        this.moduleName = moduleName;
    }

    public String getModuleName() {
        return moduleName;
    }

    public static BasicEventModuleEnum of(String moduleName) {
        return moduletMap.get(moduleName);
    }

}
