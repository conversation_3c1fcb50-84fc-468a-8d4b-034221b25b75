
package com.ke.chat.tag.api.bean;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 工具卡片，bella层定义的类，用于工具前端内容展示
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class CardTable {

    private int cardId;

    /**
     * 工具输出内容
     */
    private String data;

    /**
     * 卡片模板
     */
    private String template;

    /**
     * 卡片跳转链接
     */
    private String cardUrl;

    private operationNext operation;


    @Data
    public static class operationNext {
        /**
         * 输入
         */
        private String type;

        /**
         * 输出信息
         */
        public Object methods;
    }
}
