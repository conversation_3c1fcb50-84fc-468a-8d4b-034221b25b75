package com.ke.chat.tag.api.enums;

public enum EvaluationResultFiledEnum {

    RELATION_SCORE0("0分(不相关)", 0, ""),
    RELATION_SCORE1("1分(大部分相关)", 1, ""),
    RELATION_SCORE2("2分(完全相关)", 2, ""),
    INTEGRITY_SCORE0("0分(大都丢失/冗余)", 0, ""),
    INTEGRITY_SCORE1("1分(部分丢失/冗余)", 1, ""),
    INTEGRITY_SCORE2("2分(不多不少)", 2, ""),

    USABILITY_SCORE0("无结果", 0, ""),
    USABILITY_SCORE1("有结果-结果可用", 1, ""),
    USABILITY_SCORE2("有结果-不可用-文不对题", 2, ""),
    USABILITY_SCORE3("有结果-不可用-自由发挥", 3, ""),
    USABILITY_SCORE4("有结果-不可用-信息丢失", 4, ""),
    USABILITY_SCORE5("有结果-不可用-信息冗余", 5, ""),
    USABILITY_SCORE6("有结果-不可用-反问出错", 6, ""),
    USABILITY_SCORE7("有结果-不可用-其他问题", 7, ""),

    NO_SENSITIVE_ISSUES("无敏感问题", 0, ""),
    SENSITIVE_ISSUES("命中敏感问题", 1, ""),
    NO_DISPLAY_ISSUES("无问题", 0, ""),
    RESULT_QUALITY_SCORE0("0分（完全不相关 或  信息严重丢失 )", 0, ""),
    RESULT_QUALITY_SCORE1("1分（有一点相关 或 信息部分丢失 或 信息部分冗余）", 1, ""),
    RESULT_QUALITY_SCORE2("2分（完全相关且信息完整）", 2, ""),
    RESULT_DISPLAY_SCORE0("无问题", 0, ""),
    RESULT_DISPLAY_SCORE1("有问题-文字链出错", 1, ""),
    RESULT_DISPLAY_SCORE2("有问题-出现乱码", 2, ""),
    RESULT_DISPLAY_SCORE3("有问题-格式出错", 3, ""),
    RESULT_DISPLAY_SCORE4("有问题-重复字符", 4, ""),
    //RESULT_DISPLAY_SCORE5("有问题-信息冗余",5,""),
    RESULT_DISPLAY_SCORE6("有问题-其他原因", 6, "");


    public final String key;
    public final int value;
    public final String alias;

    EvaluationResultFiledEnum(String key, int value, String alias) {
        this.key = key;
        this.value = value;
        this.alias = alias;
    }

    public String getKey() {
        return key;
    }
}
