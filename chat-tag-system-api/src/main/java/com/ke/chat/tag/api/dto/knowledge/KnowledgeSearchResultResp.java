package com.ke.chat.tag.api.dto.knowledge;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import com.ke.chat.tag.api.dto.application.ApplicationValidationReqDTO;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Data
public class KnowledgeSearchResultResp extends KnowledgeFileResponseDTO {

    private String question;

    private Integer hasPicture;

    private String creatorUcid;

    private String creatorName;

    private String answer;

    private String chunkId;

    private String chunkType;

    private List<String> cityList;

    private List<String> businessTags;

    private String updaterUcid;

    private String updaterName;

    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime updateTime;

    private List<ApplicationValidationReqDTO> applicationList;

    private Object countQA;

    private Integer isOnline;

    private String fileType;

    private String fileUrlDoc;

    private List<String> imgList;

    private String bussinessType;

    private String filePath;

    private String groupId;

    private List<String> similarQuestions;
}
