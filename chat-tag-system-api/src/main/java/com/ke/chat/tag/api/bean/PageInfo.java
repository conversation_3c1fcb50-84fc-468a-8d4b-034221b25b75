package com.ke.chat.tag.api.bean;


import java.io.Serializable;

public class PageInfo implements Serializable {

    /**
     *
     */
    private static final long serialVersionUID = 1450839884161327273L;
    /**
     * 当前页，默认第一页
     */
    private int pageNo = 1;
    /**
     * 每页记录数，默认20条
     */
    private int pageSize = 10;

    /**
     * 总记录数
     */
    private int totalRecord;
    /**
     * 总页数
     */
    private int totalPage;


    public int getPageNo() {
        return pageNo;
    }

    public void setPageNo(int pageNo) {
        this.pageNo = pageNo;
    }

    public int getPageSize() {
        return pageSize;
    }

    public void setPageSize(int pageSize) {
        if (pageSize == 10 || pageSize == 20 || pageSize == 50 || pageSize == 100) {
            this.pageSize = pageSize;
        } else if (pageSize > 100) {
            this.pageSize = pageSize;
        } else {
            this.pageSize = 10;
        }
    }

    public int getTotalRecord() {
        return totalRecord;
    }

    public void setTotalRecord(int totalRecord) {
        int totalPage;
        if (totalRecord % pageSize == 0) {
            totalPage = totalRecord / pageSize;
        } else {
            totalPage = totalRecord / pageSize + 1;
        }
        this.totalPage = totalPage;
        this.totalRecord = totalRecord;
    }

    public int getTotalPage() {
        return totalPage;
    }

    public void setTotalPage(int totalPage) {
        this.totalPage = totalPage;
    }

}
