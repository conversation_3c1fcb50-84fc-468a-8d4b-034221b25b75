package com.ke.chat.tag.api.util;

import java.util.LinkedList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/6/8 10:51 上午
 **/
public class DataTagStrUtils {

    public static List<String> splitWithGivenLength(String content, int length) {
        List<String> splitStrings = new LinkedList<>();
        //循环不变式
        //0<=左边界<右边界<=content.length
        //0<右边界-左边界<=length
        int start = 0;
        while (start + length <= content.length()) {
            splitStrings.add(content.substring(start, start + length));
            start += length;
        }
        if (start < content.length()) {
            splitStrings.add(content.substring(start));
        }

        return splitStrings;
    }
}
