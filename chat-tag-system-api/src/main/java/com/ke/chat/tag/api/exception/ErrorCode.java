package com.ke.chat.tag.api.exception;


import com.ke.boot.common.exception.BusinessException;
import org.apache.commons.lang3.StringUtils;

public enum ErrorCode {

    DEPENDENCIES_ERROR("10000", "外部依赖异常"),


    // 标注模块
    BATCH_ASSIGN_LOCK_FAIL("20000", "当前有其他管理员在分配，请稍候再试"),
    ASSIGN_UCID_LIST_SIZE_ERROR("20001", "单次最多可以下发1000人，请调整"),
    TASK_NO_LIST_SIZE_ERROR("20002", "单次最多可以下发1000个任务，请调整"),
    ASSIGN_UCID_GREATE_THAN_TASK_SIZE("20003", "当前下发的任务数量小于已添加的人数，任务数量不足，请调整 "),
    TASK_EDN_TIME_ERROR("20005", "分配截止时间错误！"),
    USER_STATUS_ERROR("20006", "存在系统号无效或者非在职在岗，请删除后再提交！"),
    TASK_STATUS_NOT_READY_ASSIGN("20007", "选择任务中存在状态错误的任务，不可下发，请重新选择任务"),
    TASK_EMPTY("20008", "可分配的任务数量为空,请重新选择"),
    ASSIGN_MAX_SIZE_ERROR("20009", "每人单次最多分配5000个任务"),
    ASSIGN_ERROR("20010", "以下人员分配失败！"),

    USER_ERROR("40000", "使用方异常"),
    UNAUTHORIZED("40001", "权限不足"),
    INVALID_ARGUMENT("40002", "参数不合法"),


    SERVER_ERROR("50000", "服务异常"),
    KAFKA_PRODUCER_SEND_FAIL("50001", "kafka生产者发送消息异常"),

    // chunk
    CHUNK_PARAM_ERROR("60001", "切片参数错误"),
    CHUNK_EMPTY_ERROR("60002", "切片为空错误"),

    // biz
    BIZ_KNOWLEDGE_AUTH_ERROR("70001", "授权错误"),
    BIZ_KNOWLEDGE_PARAM_ERROR("70002", "API参数错误"),
    BIZ_KNOWLEDGE_CONTENT_ERROR("70003", "内容获取异常"),
    BIZ_KNOWLEDGE_CREATE_ERROR("70004", "内容创建异常"),
    BIZ_KNOWLEDGE_URL_ILLEGAL("70005", "url不合法"),
    BIZ_KNOWLEDGE_RETRIEVE_ERROR("71001", "检索异常"),


    NO_PERMISSION("8000001", "无权限"),
    ;


    public final String code;
    public final String defaultMsg;

    public String getCode() {
        return code;
    }

    public String getDefaultMsg() {
        return defaultMsg;
    }

    ErrorCode(String code, String defaultMsg) {
        this.code = code;
        this.defaultMsg = defaultMsg;
    }


    public BusinessException toBizException() {
        return new BusinessException(this.code, this.defaultMsg);
    }

    public BusinessException toBizException(String message) {
        String errMsg = StringUtils.isEmpty(message) ? defaultMsg : message;
        return new BusinessException(this.code, errMsg);
    }

}
