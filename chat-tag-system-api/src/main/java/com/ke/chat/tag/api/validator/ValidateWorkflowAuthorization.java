package com.ke.chat.tag.api.validator;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;
import org.springframework.web.server.ResponseStatusException;

import javax.servlet.http.HttpServletRequest;

/**
 * function:
 *
 * <AUTHOR>
 */
@Aspect
@Component
@Slf4j
public class ValidateWorkflowAuthorization {

    @Value("${workflow.bearer}")
    private String auth;

    private final HttpServletRequest request;


    public ValidateWorkflowAuthorization(HttpServletRequest request) {
        this.request = request;
    }

    @Around("@annotation(com.ke.chat.tag.api.validator.annotation.WorkflowAuthorization)")
    public Object authorize(ProceedingJoinPoint joinPoint) throws Throwable {
        String requestAuth = request.getHeader("Authorization");
        if (!StringUtils.equals(requestAuth, auth)) {
            LOGGER.error("Authorization failed. Provided auth: {}", requestAuth);
            throw new ResponseStatusException(HttpStatus.UNAUTHORIZED, "Unauthorized");
        }
        return joinPoint.proceed();
    }

}
