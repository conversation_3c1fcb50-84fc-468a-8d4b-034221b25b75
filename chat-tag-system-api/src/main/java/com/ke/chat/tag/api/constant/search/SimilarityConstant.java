package com.ke.chat.tag.api.constant.search;

import java.util.TreeMap;

public class SimilarityConstant {

    private SimilarityConstant() {

    }

    public static final TreeMap<Float, Float> DEFAULT_TRANS_SCORE2SCALE_MAP;

    public static final TreeMap<Float, Float> DEFAULT_TRANS_SCALE2SCORE_MAP;

    static {
        DEFAULT_TRANS_SCORE2SCALE_MAP = new TreeMap<>();
        DEFAULT_TRANS_SCORE2SCALE_MAP.put(0f, 0f);
        DEFAULT_TRANS_SCORE2SCALE_MAP.put(0.4f, 0.8f);
        DEFAULT_TRANS_SCORE2SCALE_MAP.put(1f, 1f);

        DEFAULT_TRANS_SCALE2SCORE_MAP = new TreeMap<>();
        DEFAULT_TRANS_SCALE2SCORE_MAP.put(0f, 0f);
        DEFAULT_TRANS_SCALE2SCORE_MAP.put(0.8f, 0.4f);
        DEFAULT_TRANS_SCALE2SCORE_MAP.put(1f, 1f);
    }
}
