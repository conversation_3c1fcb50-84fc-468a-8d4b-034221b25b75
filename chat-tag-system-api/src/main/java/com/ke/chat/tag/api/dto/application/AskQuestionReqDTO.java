package com.ke.chat.tag.api.dto.application;

import com.google.common.collect.Lists;
import com.ke.boot.common.exception.BusinessException;
import com.ke.chat.tag.api.bean.assistant.OpenAIContent;
import com.ke.chat.tag.api.bean.message.BellaContent;
import com.ke.chat.tag.api.dto.assistant.tool.BellaTool;
import com.ke.chat.tag.api.enums.AssistantApplicationEnvEnum;
import com.ke.chat.tag.api.enums.ConversationRecordSourceEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;
import org.apache.commons.collections.CollectionUtils;

import javax.annotation.Nullable;
import javax.validation.constraints.NotNull;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@SuperBuilder
public class AskQuestionReqDTO {

    @NotNull(message = "applicationId must not be blank")
    protected Long applicationId;

    /**
     * 目前已知小程序再用此字段
     */
    @NotNull(message = "content must not be blank")
    @Deprecated
    protected String content;

    protected String runId;

    @Nullable
    protected String scene;    // 会话场景

    @Nullable
    protected String convId; // 会话id

    @Nullable
    protected List<String> fileIds; // 本次会话关联文件

    @Nullable
    protected String role;    // 本次会话设置角色信息（会覆盖instructions）

    @Nullable
    protected List<BellaTool> tools; // 会话关联tools

    @Nullable
    protected String env = AssistantApplicationEnvEnum.PROD.desc;  // 会话环境

    @Nullable
    private String model;

    @Nullable
    private Float temperature;

    @Deprecated
    private String messageId;

    private String threadId;

    /**
     * at的智能体ID
     */
    private Long atApplicationId;

    /**
     * 多模型模式下组ID，有前端传递一个uuid
     */
    private String groupId;

    private String source;
    private ConversationRecordSourceEnum recordSource;

    private String saveMode;

    private List<OpenAIContent> contents;

    private List<BellaContent> bellaContents;

    private Boolean stream;

    @Nullable
    private String userId;

    private Integer contextNumber;

    public void init() {
        content = content.trim();
        if (content.length() == 0) {
            throw new BusinessException("400", "问题内容不能为空");
        }
    }

    public List<String> getToolTypes() {
        // assistant-api对应的工具为null和空数组处理逻辑不同，所以要分开处理
        if (this.getTools() == null) {
            return null;
        }
        if (this.getTools().isEmpty()) {
            return Collections.emptyList();
        }
        return this.getTools().stream()
            .filter(tool -> tool != null && tool.getType() != null) // 过滤掉 null 的工具和类型
            .map(BellaTool::getType) // 映射到工具类型
            .collect(Collectors.toList()); // 收集到列表中
    }

    /**
     * 获取 rag 工具的过滤器
     */
    public List<BellaTool.Filters> getRagToolFilters() {
        return Optional.ofNullable(this.getTools()).orElse(Lists.newArrayList()).stream()
            .filter(tool -> Objects.nonNull(tool) && "rag".equals(tool.getType()) && CollectionUtils.isNotEmpty(tool.getFilters()))
            .flatMap(tool -> tool.getFilters().stream())
            .collect(Collectors.toList());
    }
}
