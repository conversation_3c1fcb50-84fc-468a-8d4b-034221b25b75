package com.ke.chat.tag.api.dto.knowledge;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/11/21
 */
@Data
public class PreloadedKnowledgeDTO {

    /**
     * 主键
     */
    private Long id;

    /**
     * 文件ID
     */
    private String fileId;

    /**
     * 文件名称
     */
    private String fileName;

    /**
     * 问题
     */
    private List<String> questions;

    /**
     * 答案
     */
    private String answer;

    /**
     * 城市列表
     */
    private List<String> cityList;

    /**
     * 标签
     */
    private List<String> tagList;

    /**
     * 图片
     */
    private List<String> imageList;

    /**
     * 对外标识的ID
     */
    private String outerUuid;

    /**
     * 知识条目关联的groupId
     */
    private String groupId;

    /**
     * 状态：默认0未应用，-1删除，1应用
     */
    private Integer status;

    /**
     * 创建者
     */
    private String createUcid;

    /**
     * 修改者
     */
    private String updateUcid;

    /**
     * 修改者名称
     */
    private String updateUserName;

    /**
     * 创建者名称
     */
    private String createUserName;

    /**
     * 创建时间
     */
    private Long ctime;

    /**
     * 更新时间
     */
    private Long mtime;
}
