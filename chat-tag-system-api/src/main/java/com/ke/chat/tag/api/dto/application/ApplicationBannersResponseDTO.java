package com.ke.chat.tag.api.dto.application;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ApplicationBannersResponseDTO {

    private List<BannerInfo> banners;

    @Data
    @Builder
    public static class BannerInfo {
        private String imageUrl;
        private String url;
        private String desc;
    }

}
