package com.ke.chat.tag.api.enums.partner;

import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * function: 伙伴服务组织类型枚举
 *
 * <AUTHOR>
 */
@Getter
public enum PartnerOrgTypeEnum {

    ZHENG_ZHUANG("zheng-zhuang", "整装"),

    JING_JI("jing-ji", "经纪"),

    HUI_JU("hui-ju", "惠居"),

    CHAN_YAN("chan-yan", "产研"),

    ZHI_NENG("zhi-neng", "职能");

    private final String code;

    private final String name;

    public static final List<String> ALL_ORG_TYPE_CODES =
        Arrays.stream(PartnerOrgTypeEnum.values())
            .map(PartnerOrgTypeEnum::getCode)
            .collect(Collectors.toList());


    PartnerOrgTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static String getNameByCode(String code) {
        for (PartnerOrgTypeEnum partnerOrgTypeEnum : PartnerOrgTypeEnum.values()) {
            if (partnerOrgTypeEnum.code.equals(code)) {
                return partnerOrgTypeEnum.name;
            }
        }
        return null;
    }


}
