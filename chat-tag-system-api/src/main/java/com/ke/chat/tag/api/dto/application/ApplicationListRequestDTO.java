package com.ke.chat.tag.api.dto.application;

import com.google.common.collect.Lists;
import com.ke.chat.tag.api.util.JsonUtil;
import lombok.Data;

import java.util.List;

@Data
public class ApplicationListRequestDTO {

    /**
     * 标签，默认recommend
     * 新含义：通用标签(兼容小程序)
     */
    private String tag = "";

    /**
     * cursor，上一页最后一个cursor，为空则请求第一页；
     * 旧的翻页参数，后续会下线（推动小程序改动）
     * {@code @Deprecated}
     */
    private Long applicationId;

    /**
     * 页码
     */
    private Integer pageNum = 1;
    /**
     * 页大小，默认20
     */
    private Integer pageSize = 20;

    private String criteria;

    /**
     * 事业线标签
     */
    private List<String> orgTags = Lists.newArrayList();

    /**
     * 职能标签
     */
    private List<String> functionTags = Lists.newArrayList();

    /**
     * 城市标签
     */
    private List<String> cityTags = Lists.newArrayList();
    /**
     * 可复制智能体标签
     */
    private List<String> enableCopyAppTags = Lists.newArrayList();
    /**
     * 智能体模式
     */
    private String mode;

    /**
     * 排序规则: 最热:heat 最新：createTime
     */
    private String sort = "heat";

    public boolean firstPage() {
        return applicationId == null;
    }

    public int pageSize(int defaultPageSize) {
        if (pageSize == null) {
            return defaultPageSize;
        }
        return pageSize;
    }

    @Override
    public String toString() {
        return JsonUtil.parseBean2Str(this);
    }
}
