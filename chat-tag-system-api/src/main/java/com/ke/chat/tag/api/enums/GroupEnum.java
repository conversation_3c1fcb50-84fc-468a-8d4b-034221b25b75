package com.ke.chat.tag.api.enums;

public enum GroupEnum {
    //我创建的
    CREATED(1, "我创建的"),

    //我协作的
    COOPERATED(2, "我协作的"),

    //和我无关
    UNRELATED(3, "和我无关"),

    QW(4, "企微回收"),

    CHAT(5, "对话");

    public final Integer code;
    public final String desc;

    GroupEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

}
