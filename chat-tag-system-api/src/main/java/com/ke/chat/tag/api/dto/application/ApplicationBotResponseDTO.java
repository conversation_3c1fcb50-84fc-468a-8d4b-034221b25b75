package com.ke.chat.tag.api.dto.application;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ApplicationBotResponseDTO {

    private String avatar;    // 头像url链接
    private String botName;    // 机器人名称
    private String botUcid;    // 机器人系统号
    private String positionName; // 岗位名称
    private Boolean created; // 应用是否关联机器人
    private Integer groupCount; // 若已关联，加入群聊数
    private Integer groupLimit; // 应用可关联群聊数上限
}
