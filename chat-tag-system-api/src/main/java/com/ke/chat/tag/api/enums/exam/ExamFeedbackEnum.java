package com.ke.chat.tag.api.enums.exam;

import lombok.Getter;

@Getter
public enum ExamFeedbackEnum {

    MODIFY_DESC((byte) 1, "修改说明："),
    MODIFY_CONTENT((byte) 2, "修正后的内容："),
    UNUSABLE_REASON((byte) 3, "不可用原因：");

    private Byte feedbackCode;
    private String feedbackDesc;

    ExamFeedbackEnum(Byte feedbackCode, String feedbackDesc) {
        this.feedbackCode = feedbackCode;
        this.feedbackDesc = feedbackDesc;
    }
}
