package com.ke.chat.tag.api.dto.application;

import com.ke.chat.tag.api.response.BasePageRequest;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import javax.annotation.Nullable;
import java.time.LocalDateTime;


/**
 * 应用信息查询实体
 *
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class OperationApplicationInfosReqDTO extends BasePageRequest {

    /**
     * 检索内容
     * 支持模糊匹配
     */
    @Nullable
    private String criteria;

    @Nullable
    private String visibility;

    @Nullable
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime beginTime;

    @Nullable
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endTime;
}
