package com.ke.chat.tag.api.dto.knowledge;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class KnowledgeItemInfo {

    /**
     * id,取同组最小的
     */
    private Integer id;


    /**
     * 问题
     */
    private String question;

    /**
     * 答案
     */
    private String answer;

    /**
     * group id
     */
    private String groupId;

    /**
     * 是否在线
     */
    private int isOnline;

    /**
     * 城市列表
     */
    private List<String> cityList;

    /**
     * 图片列表
     */
    private List<String> imgList;

    /**
     * 业务标签
     */
    private List<String> businessTags;

    /**
     * 相识问
     */
    private List<String> similarQuestions;
}
