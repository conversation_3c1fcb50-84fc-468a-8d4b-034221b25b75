package com.ke.chat.tag.api.dto.application;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.ke.chat.tag.api.dto.application.WorkflowDTO.Workflow;
import com.ke.chat.tag.api.dto.assistant.ChatflowBaseQaConfig;
import com.ke.chat.tag.api.dto.knowledge.KnowledgeFileResponseDTO;
import com.ke.chat.tag.api.enums.application.ApplicationMode;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class BaseApplicationRespDTO {

    /**
     * 是否有编辑权限
     */
    private Boolean editable;

    /**
     * 应用名称
     */
    private String name;

    /**
     * 应用介绍
     */
    private String description;

    /**
     * 图标url
     */
    private String icon;

    /**
     * 模型
     */
    private String model;

    /**
     * 是否开启欢迎词 true: 开启；false: 关闭
     */
    private Boolean welcomeWordsEnabled;

    /**
     * 欢迎词
     */
    private String welcomeWords;

    /**
     * 示例提问集 ListJson结构
     */
    private List<String> welcomeWordsExampleQuestions;

    /**
     * 角色介绍
     */
    private String roleInstruction;

    /**
     * 是否开启提示词 true：开启；false：关闭
     */
    private Boolean prompterEnabled;

    /**
     * 提示词
     */
    private List<String> prompts;

    /**
     * 可见性 public:公开；private：私有
     */
    private String visibility;

    /**
     * 应用类型
     */
    private String type;

    /**
     * 知识集合
     */
    private List<KnowledgeFileResponseDTO> files;

    /**
     * 设置昵称
     */
    private String nickName;

    /**
     * 是否可以debug
     */
    private Boolean debuggable = false;

    /**
     * 检索设置
     */
    private SearchParam SearchParam;

    /**
     * 热度 注：webapp（即广告位应用）暂不支持
     */
    private String heat;

    /**
     * 标签
     */
    private List<String> tags;

    /**
     * 绑定的协作组
     */
    private String groupName;

    /**
     * 绑定的协作组别名
     */
    private String groupAlias;

    /**
     * 协作组信息：我创建的，我协作的
     */
    private String groupType;

    /**
     * 绑定的内部工具
     */
    private List<String> tools;

    private List<ToolDisplay> toolDisplay;

    private WorkflowInfo workflowInfo;

    /**
     * 存在草稿尚未发布
     */
    private int isDraftUnpublished;

    /**
     * 对外的APPID
     */
    private Long outerApplicationId;

    /**
     * 本人是否收藏
     * 注：webapp（即广告位应用）暂不支持
     */
    private boolean collected;

    /**
     * 模型对应的输入限制
     */
    private Integer inputLimit;

    /**
     * 是否有编辑权限
     */

    private String instructionType;

    private String mode = ApplicationMode.LLM.getName();
    /**
     * owner所在组织名称
     */
    private String orgName;

    /**
     * 整体所属空间
     */
    private String spaceCode;

    /**
     * 是智能体拥有者（true表示是，false表示不是）
     */
    @JsonProperty("isOwnerUser")
    private Boolean isOwnerUser;

    /**
     * 事业线标签
     */
    private List<String> orgTags;

    /**
     * 职能标签
     */
    private List<String> functionTags;

    /**
     * 通用标签
     */
    private List<String> universalTags;

    /**
     * 城市标签
     */
    private List<String> cityTags;


    private ChatflowBaseQaConfig chatflowBaseQaConfig;


    /**
     * 可复制智能体标签
     */
    private List<String> enableCopyAppTags;
    /**
     * 来源应用id
     */
    private Long fromAppId;

    public void setWorkflows(List<Workflow> workflows) {
        this.setWorkflowInfo(new WorkflowInfo(workflows));
    }

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class ToolDisplay {

        private String toolName;

        private String toolCnName;

        /**
         * 工具绑定有效状态，true表示有效，false表示无效
         */
        private Boolean toolBindValid;
        private Integer order;
    }

    public static BaseApplicationRespDTO filterFileUrlAndIconUrl(BaseApplicationRespDTO baseApplicationRespDTO) {
        Optional.ofNullable(baseApplicationRespDTO.getFiles())
            .ifPresent(files -> files.forEach(file -> {
                file.setFileUrl(null);
                file.setIconUrl(null);
            }));
        return baseApplicationRespDTO;
    }

}
