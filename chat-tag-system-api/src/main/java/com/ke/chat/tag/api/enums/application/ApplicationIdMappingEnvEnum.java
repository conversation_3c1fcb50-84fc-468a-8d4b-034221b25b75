package com.ke.chat.tag.api.enums.application;

import lombok.Getter;

/**
 * function:
 *
 * <AUTHOR>
 */
@Getter
public enum ApplicationIdMappingEnvEnum {

    TEST("test", "id映射环境-test"),

    PROD("prod", "id映射环境-prod");

    private final String name;
    private final String desc;

    ApplicationIdMappingEnvEnum(String name, String desc) {
        this.name = name;
        this.desc = desc;
    }

}
