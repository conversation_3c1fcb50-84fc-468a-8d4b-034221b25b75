package com.ke.chat.tag.api.bean.rss;

import com.ke.chat.tag.api.util.JsoupUtil;
import com.ke.risk.safety.common.util.date.DateUtil;
import com.thoughtworks.xstream.annotations.XStreamAlias;
import com.thoughtworks.xstream.annotations.XStreamAsAttribute;
import com.thoughtworks.xstream.annotations.XStreamImplicit;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

@Getter
@Setter
@Slf4j
@XStreamAlias("rss")
public class RSSResult {

    @XStreamAlias("xmlns:atom")
    @XStreamAsAttribute
    private String xmlnsAtom;

    @XStreamAlias("version")
    @XStreamAsAttribute
    private String version;

    @XStreamAlias("channel")
    private RSSChannel channel;

    @Getter
    @Setter
    @Slf4j
    @XStreamAlias("channel")
    public static class RSSChannel {

        @XStreamAlias("title")
        private String title;

        @XStreamAlias("link")
        private String link;

        @XStreamAlias("atom:link")
        private String atomLink;

        @XStreamAlias("description")
        private String description;

        @XStreamAlias("generator")
        private String generator;

        @XStreamAlias("webMaster")
        private String webMaster;

        @XStreamAlias("language")
        private String language;

        @XStreamAlias("image")
        private RSSImage image;

        @XStreamAlias("lastBuildDate")
        private String lastBuildDate;

        @XStreamAlias("ttl")
        private Integer ttl;

        @XStreamImplicit(itemFieldName = "item")
        private List<RSSItem> items;

        public boolean invalid() {
            return items == null || items.isEmpty();
        }
    }

    @Getter
    @Setter
    @Slf4j
    @XStreamAlias("image")
    public static class RSSImage {

        @XStreamAlias("url")
        private String url;

        @XStreamAlias("title")
        private String title;

        @XStreamAlias("link")
        private String link;
    }

    @Getter
    @Setter
    @Slf4j
    @XStreamAlias("item")
    public static class RSSItem {

        @XStreamAlias("title")
        private String title;

        @XStreamAlias("description")
        private String description;

        @XStreamAlias("pubDate")
        private String pubDate;

        @XStreamAlias("guid")
        private String guid;

        @XStreamAlias("link")
        private String link;

        @XStreamAlias("author")
        private String author;

        public long time() {
            try {
                String format = "EEE, d MMM yyyy HH:mm:ss 'GMT'";
                return DateUtil.getDate(pubDate, format).getTime();
            } catch (Exception e) {
                LOGGER.error("time()", e);
                return 0;
            }
        }

        public String description() {
            return JsoupUtil.text(description);
        }

    }

    public boolean invalid() {
        return channel == null || channel.invalid();
    }

}
