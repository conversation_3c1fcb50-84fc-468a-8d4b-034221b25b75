package com.ke.chat.tag.api.enums;

/**
 * <AUTHOR>
 * @date 2023/10/11 5:09 下午
 **/
public enum AsyncTaskStatusEnum {
    UN_PROCESS("UN_PROCESS", "待处理"),
    PROCESSING("PROCESSING", "处理中"),
    PROCESS_SUCCESS("PROCESS_SUCCESS", "处理成功"),
    PROCESS_FAIL("PROCESS_FAIL", "处理失败");
    public final String code;
    public final String desc;

    AsyncTaskStatusEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}
