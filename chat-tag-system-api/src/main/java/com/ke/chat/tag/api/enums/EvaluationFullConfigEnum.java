package com.ke.chat.tag.api.enums;

public enum EvaluationFullConfigEnum {
    FULL_PROCESS_LABEL_CONFIG_HEAD(0, "全流程label_config头部config", "<View>\\n    <style>\\n        .sub_module_1 {\\n            border-style: solid;\\n            display: inline-flexbox;\\n            flex-direction: row;\\n        }\\n\\n        .sub_module_sticky {\\n            border-style: solid;\\n            z-index: 9999;\\n            background-color: rgba(255, 255, 255, 1);\\n            position: sticky;\\n            top: 0;\\n            max-height: 300px; /* 设置固定高度，您可以根据需要调整 */\\n            overflow-y: auto; /* 允许垂直滚动 */\\n        }\\n\\n        .sub_module {\\n            border-style: solid;\\n        }\\n\\n        .introduction_key {\\n            font-weight: bold;\\n            font-size: large;\\n        }\\n\\n        .introduction_score {\\n            font-weight: bold;\\n            font-size: large;\\n            color: blue;\\n        }\\n\\n        .introduction_source {\\n            font-weight: bold;\\n            color: grey;\\n        }\\n\\n        .introduction_value {\\n            font-size: large;\\n        }\\n\\n        .small_container_inline {\\n            display: flex;\\n            flex-direction: row;\\n        }\\n\\n        .small_container_block {\\n            border: 1px solid #ccc;\\n            overflow: hidden;\\n            /* 添加边框，1像素宽度，灰色 */\\n        }\\n\\n        .small_container_block_flex {\\n            border: 1px solid #ccc;\\n            overflow: hidden;\\n        }\\n\\n        .small_block_flex {\\n            border: 1px solid #ccc;\\n        }\\n\\n        .model_name {\\n            font-size: xx-large;\\n            color: rebeccapurple;\\n            font-weight: bold;\\n        }\\n\\n        .model_question {\\n            font-size: large;\\n            font-weight: bold;\\n        }\\n\\n        .model_sub_question {\\n            font-size: small;\\n            font-weight: bold;\\n        }\\n    </style>\\n    <!-- 原信息 -->\\n    <View className=\\\"sub_module_1\\\">\\n        <!-- 业务场景名称 -->\\n        <View className=\\\"small_container_inline\\\">\\n            <View className=\\\"introduction_key\\\" display=\\\"inline\\\">\\n                <Text name=\\\"business_scene_key\\\" value=\\\"业务场景：\\\" />\\n            </View>\\n            <View className=\\\"introduction_value\\\">\\n                <Text name=\\\"business_scene_value\\\" value=\\\"$meta_data.application_full_ability_template.application.business_scene\\\" />\\n            </View>\\n        </View>\\n    </View>\\n\\n    <!-- 问题相关 -->\\n    <View>\\n        <View className=\\\"sub_module_sticky\\\">\\n            <View className=\\\"small_container_block_flex\\\">\\n                <!-- query类型 -->\\n                <View className=\\\"small_block_flex\\\">\\n                    <View className=\\\"introduction_key\\\" display=\\\"inline\\\">\\n                        <Text name=\\\"query_type_key\\\" value=\\\"Query类型：\\\" />\\n                    </View>\\n                    <View className=\\\"introduction_value\\\" display=\\\"inline\\\">\\n                        <Text name=\\\"query_type_value\\\" value=\\\"$meta_data.application_full_ability_template.application.input_type\\\" />\\n                    </View>\\n                </View>\\n                <!-- 用户query -->\\n                <View className=\\\"small_block_flex\\\">\\n                    <View className=\\\"introduction_key\\\" display=\\\"inline\\\">\\n                        <Text name=\\\"user_query\\\" value=\\\"用户Query：\\\" />\\n                    </View>\\n                    <View className=\\\"introduction_value\\\" display=\\\"inline\\\">\\n                        <Text name=\\\"user_query_value\\\" value=\\\"$meta_data.application_full_ability_template.application.input\\\" />\\n                    </View>\\n                </View>\\n            </View>\\n            <!-- 标准答案 -->\\n            <View className=\\\"small_container_block\\\">\\n                <View className=\\\"introduction_key\\\" display=\\\"inline\\\">\\n                    <Text name=\\\"standard_answer\\\" value=\\\"标准答案\\\" />\\n                </View>\\n                <View className=\\\"introduction_value\\\" display=\\\"inline\\\">\\n                    <Text name=\\\"standard_answer_value\\\" value=\\\"$meta_data.application_full_ability_template.application.standard_output\\\" />\\n                </View>\\n            </View>\\n        </View>\\n        <View className=\\\"sub_module\\\">\\n            "),

    FULL_PROCESS_LABEL_CONFIG_RECALL(0, "全流程的召回label_config", "\\n            <View className=\\\"small_container_block\\\">\\n                <View className=\\\"introduction_key\\\" display=\\\"inline\\\">\\n                    <Text name=\\\"top%s_recall\\\" value=\\\"Top%s召回\\\" />\\n                </View>\\n                <View className=\\\"introduction_score\\\" display=\\\"inline\\\">\\n                    <Text name=\\\"top%s_recall_score\\\" value=\\\"$meta_data.application_full_ability_template.application.embedding_model.recalls[%s].score\\\" />\\n                </View>\\n                <View className=\\\"introduction_source\\\" display=\\\"inline\\\">\\n                    <Text name=\\\"top%s_recall_source\\\" value=\\\"$meta_data.application_full_ability_template.application.embedding_model.recalls[%s].source\\\" />\\n                </View>\\n                <View className=\\\"introduction_value\\\">\\n                    <Text name=\\\"top%s_recall_content\\\" value=\\\"$meta_data.application_full_ability_template.application.embedding_model.recalls[%s].content\\\" />\\n                </View>\\n                <View>\\n                    <View className=\\\"model_question\\\">\\n                        <Text name=\\\"top%s_recall_question1\\\" value=\\\"Top%s召回是否有问题\\\" />\\n                    </View>\\n                    <Choices name=\\\"top%s_recall_choices1\\\" showInline=\\\"true\\\" toName=\\\"top%s_recall\\\" choice=\\\"single\\\"\\n                             required=\\\"true\\\">\\n                        <Choice value=\\\"无问题\\\" />\\n                        <Choice value=\\\"有问题\\\" />\\n                        <Choice value=\\\"无返回\\\" />\\n                    </Choices>\\n                </View>\\n                <View visibleWhen=\\\"choice-selected\\\" whenTagName=\\\"top%s_recall_choices1\\\" whenChoiceValue=\\\"有问题\\\">\\n                    <View className=\\\"model_question\\\">\\n                        <Text name=\\\"top%s_recall_question2\\\" value=\\\"相关性\\\" />\\n                    </View>\\n                    <Choices name=\\\"top%s_recall_choices2\\\" showInline=\\\"true\\\" toName=\\\"top%s_recall\\\" choice=\\\"single\\\"\\n                             required=\\\"true\\\">\\n                        <Choice value=\\\"2分(完全相关)\\\" />\\n                        <Choice value=\\\"1分(大部分相关)\\\" />\\n                        <Choice value=\\\"0分(不相关)\\\" />\\n                    </Choices>\\n                </View>\\n                <View visibleWhen=\\\"choice-selected\\\" whenTagName=\\\"top%s_recall_choices1\\\" whenChoiceValue=\\\"有问题\\\">\\n                    <View className=\\\"model_question\\\">\\n                        <Text name=\\\"top%s_recall_question3\\\" value=\\\"完整性\\\" />\\n                    </View>\\n                    <Choices name=\\\"top%s_recall_choices3\\\" showInline=\\\"true\\\" toName=\\\"top%s_recall\\\" choice=\\\"single\\\"\\n                             required=\\\"true\\\">\\n                        <Choice value=\\\"2分(不多不少)\\\" />\\n                        <Choice value=\\\"1分(部分丢失/冗余)\\\" />\\n                        <Choice value=\\\"0分(大都丢失/冗余)\\\" />\\n                    </Choices>\\n                </View>\\n            </View>\\n            "),
    FULL_PROCESS_LABEL_CONFIG_RECALL_END(0, "全流程label_config召回结束", "</View>\\n        "),
    FULL_PROCESS_LABEL_CONFIG_MODEL_BEGIN(0, "全流程label_config模型开始", "<View className=\\\"sub_module\\\">\\n            "),

    FULL_PROCESS_LABEL_CONFIG_MODEL(0, "模型", "\\n            <View className=\\\"small_container_block\\\">\\n                <View className=\\\"model_name\\\">\\n                    <Text name=\\\"%s_model_name\\\" value=\\\"$meta_data.application_full_ability_template.application.chat_models[%s].name\\\" />\\n                </View>\\n                <View className=\\\"introduction_key\\\" display=\\\"inline\\\">\\n                    <Text name=\\\"%s_model_answer\\\" value=\\\"返回答案：\\\" />\\n                </View>\\n                <View className=\\\"introduction_value\\\">\\n                    <Text name=\\\"%s_model_answer_value\\\" value=\\\"$meta_data.application_full_ability_template.application.chat_models[%s].actual_output\\\" />\\n                </View>\\n                <View>\\n                    <View className=\\\"model_question\\\">\\n                        <Text name=\\\"%s_model_question_1\\\" value=\\\"返回结果可用性如何？\\\" />\\n                    </View>\\n                    <Choices name=\\\"%s_model_answer_choices1\\\" toName=\\\"%s_model_answer\\\" choice=\\\"single\\\" required=\\\"true\\\">\\n                        <Choice value=\\\"无结果\\\" />\\n                        <Choice value=\\\"有结果-结果可用\\\" />\\n                        <Choice value=\\\"有结果-不可用-文不对题\\\" />\\n                        <Choice value=\\\"有结果-不可用-自由发挥\\\" />\\n                        <Choice value=\\\"有结果-不可用-信息丢失\\\" />\\n                        <Choice value=\\\"有结果-不可用-信息冗余\\\" />\\n                        <Choice value=\\\"有结果-不可用-反问出错\\\" />\\n                        <Choice value=\\\"有结果-不可用-其他原因\\\" />\\n                    </Choices>\\n                </View>\\n                <View visibleWhen=\\\"choice-unselected\\\" whenTagName=\\\"%s_model_answer_choices1\\\" whenChoiceValue=\\\"无结果\\\">\\n                    <View className=\\\"model_question\\\">\\n                        <Text name=\\\"%s_model_question_2\\\" value=\\\"结果展示是否有问题？\\\" />\\n                    </View>\\n                    <Choices name=\\\"%s_model_answer_choices2\\\" toName=\\\"%s_model_answer\\\" choice=\\\"single\\\" required=\\\"true\\\">\\n                        <Choice value=\\\"无问题\\\" />\\n                        <Choice value=\\\"有问题-文字链出错\\\" />\\n                        <Choice value=\\\"有问题-出现乱码\\\" />\\n                        <Choice value=\\\"有问题-格式出错\\\" />\\n                        <Choice value=\\\"有问题-重复字符\\\" />\\n                        <Choice value=\\\"有问题-其他原因\\\" />\\n                    </Choices>\\n                </View>\\n                <!--  -->\\n                <View visibleWhen=\\\"choice-unselected\\\" whenTagName=\\\"%s_model_answer_choices1\\\" whenChoiceValue=\\\"无结果\\\">\\n                    <View className=\\\"model_question\\\">\\n                        <Text name=\\\"%s_model_question_3\\\" value=\\\"当前结果的回复质量如何？\\\" />\\n                    </View>\\n                    <Choices name=\\\"%s_model_answer_choices3\\\" showInline=\\\"true\\\" toName=\\\"%s_model_answer\\\" choice=\\\"single\\\" required=\\\"true\\\">\\n                        <Choice value=\\\"2分（完全相关且信息完整）\\\" />\\n                        <Choice value=\\\"1分（有一点相关 或 信息部分丢失 或 信息部分冗余）\\\" />\\n                        <Choice value=\\\"0分（完全不相关 或  信息严重丢失 )\\\" />\\n                    </Choices>\\n                </View>\\n                <View visibleWhen=\\\"choice-unselected\\\" whenTagName=\\\"%s_model_answer_choices1\\\" whenChoiceValue=\\\"无结果\\\">\\n                    <View className=\\\"model_question\\\">\\n                        <Text name=\\\"%s_model_question_4\\\" value=\\\"当前结果是否命中敏感问题？\\\" />\\n                    </View>\\n                    <Choices name=\\\"%s_model_answer_choices4\\\" showInline=\\\"true\\\" toName=\\\"%s_model_answer\\\" choice=\\\"single\\\" required=\\\"true\\\">\\n                        <Choice value=\\\"命中敏感问题\\\" />\\n                        <Choice value=\\\"无敏感问题\\\" />\\n                    </Choices>\\n                </View>\\n            </View>\\n"),
    FULL_PROCESS_LABEL_CONFIG_MODEL_HIDE(1, "模型隐藏", "\\n            <View className=\\\"small_container_block\\\">\\n                <View className=\\\"model_name\\\">\\n                    <Text name=\\\"%s_model_name\\\" value=\\\"模型%s\\\" />\\n                </View>\\n                <View className=\\\"introduction_key\\\" display=\\\"inline\\\">\\n                    <Text name=\\\"%s_model_answer\\\" value=\\\"返回答案：\\\" />\\n                </View>\\n                <View className=\\\"introduction_value\\\">\\n                    <Text name=\\\"%s_model_answer_value\\\" value=\\\"$meta_data.application_full_ability_template.application.chat_models[%s].actual_output\\\" />\\n                </View>\\n                <View>\\n                    <View className=\\\"model_question\\\">\\n                        <Text name=\\\"%s_model_question_1\\\" value=\\\"返回结果可用性如何？\\\" />\\n                    </View>\\n                    <Choices name=\\\"%s_model_answer_choices1\\\" toName=\\\"%s_model_answer\\\" choice=\\\"single\\\" required=\\\"true\\\">\\n                        <Choice value=\\\"无结果\\\" />\\n                        <Choice value=\\\"有结果-结果可用\\\" />\\n                        <Choice value=\\\"有结果-不可用-文不对题\\\" />\\n                        <Choice value=\\\"有结果-不可用-自由发挥\\\" />\\n                        <Choice value=\\\"有结果-不可用-信息丢失\\\" />\\n                        <Choice value=\\\"有结果-不可用-信息冗余\\\" />\\n                        <Choice value=\\\"有结果-不可用-反问出错\\\" />\\n                        <Choice value=\\\"有结果-不可用-其他原因\\\" />\\n                    </Choices>\\n                </View>\\n                <View visibleWhen=\\\"choice-unselected\\\" whenTagName=\\\"%s_model_answer_choices1\\\" whenChoiceValue=\\\"无结果\\\">\\n                    <View className=\\\"model_question\\\">\\n                        <Text name=\\\"%s_model_question_2\\\" value=\\\"结果展示是否有问题？\\\" />\\n                    </View>\\n                    <Choices name=\\\"%s_model_answer_choices2\\\" toName=\\\"%s_model_answer\\\" choice=\\\"single\\\" required=\\\"true\\\">\\n                        <Choice value=\\\"无问题\\\" />\\n                        <Choice value=\\\"有问题-文字链出错\\\" />\\n                        <Choice value=\\\"有问题-出现乱码\\\" />\\n                        <Choice value=\\\"有问题-格式出错\\\" />\\n                        <Choice value=\\\"有问题-重复字符\\\" />\\n                        <Choice value=\\\"有问题-其他原因\\\" />\\n                    </Choices>\\n                </View>\\n                <!--  -->\\n                <View visibleWhen=\\\"choice-unselected\\\" whenTagName=\\\"%s_model_answer_choices1\\\" whenChoiceValue=\\\"无结果\\\">\\n                    <View className=\\\"model_question\\\">\\n                        <Text name=\\\"%s_model_question_3\\\" value=\\\"当前结果的回复质量如何？\\\" />\\n                    </View>\\n                    <Choices name=\\\"%s_model_answer_choices3\\\" showInline=\\\"true\\\" toName=\\\"%s_model_answer\\\" choice=\\\"single\\\" required=\\\"true\\\">\\n                        <Choice value=\\\"2分（完全相关且信息完整）\\\" />\\n                        <Choice value=\\\"1分（有一点相关 或 信息部分丢失 或 信息部分冗余）\\\" />\\n                        <Choice value=\\\"0分（完全不相关 或  信息严重丢失 )\\\" />\\n                    </Choices>\\n                </View>\\n                <View visibleWhen=\\\"choice-unselected\\\" whenTagName=\\\"%s_model_answer_choices1\\\" whenChoiceValue=\\\"无结果\\\">\\n                    <View className=\\\"model_question\\\">\\n                        <Text name=\\\"%s_model_question_4\\\" value=\\\"当前结果是否命中敏感问题？\\\" />\\n                    </View>\\n                    <Choices name=\\\"%s_model_answer_choices4\\\" showInline=\\\"true\\\" toName=\\\"%s_model_answer\\\" choice=\\\"single\\\" required=\\\"true\\\">\\n                        <Choice value=\\\"命中敏感问题\\\" />\\n                        <Choice value=\\\"无敏感问题\\\" />\\n                    </Choices>\\n                </View>\\n            </View>\\n"),


    FULL_PROCESS_LABEL_CONFIG_end(0, "全流程label_config结束", "</View>\\n    </View>\\n</View>");


    public final Integer code;
    public final String content;
    public final String config;

    EvaluationFullConfigEnum(Integer code, String content, String config) {
        this.code = code;
        this.content = content;
        this.config = config;
    }

    public String getContent() {
        return this.content;
    }

    public Integer getCode() {
        return this.code;
    }


}
