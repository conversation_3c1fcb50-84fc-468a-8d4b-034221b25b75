package com.ke.chat.tag.api.dto.application;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ApplicationTypesResp {

    /**
     * {@link com.ke.chat.tag.api.enums.application.ApplicationTypeEnum}
     * 对应枚举 name字段
     */
    private String name;

    /**
     * 前端文案，通过apollo获取
     */
    private String desc;
}
