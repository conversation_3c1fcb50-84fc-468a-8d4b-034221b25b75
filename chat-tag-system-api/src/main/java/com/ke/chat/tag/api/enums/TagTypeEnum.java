package com.ke.chat.tag.api.enums;

/**
 * <AUTHOR>
 * @date 2023/3/1 11:11 上午
 **/
public enum TagTypeEnum {
    TAG_QUESTION((byte) 1, "对指定问题的标注"),
    TAG_REPLY((byte) 2, "对指定回答的标注");

    public final Byte code;
    public final String message;

    TagTypeEnum(Byte code, String message) {
        this.code = code;
        this.message = message;
    }

    public Byte getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }
}
