package com.ke.chat.tag.api.enums;

import lombok.Getter;

@Getter
public enum ApplicationDataSourceEnum {

    Total(0, "全部"),

    BELLA(1, "Bella平台"),

    API(2, "API"),

    GROUP_BOT(3, "群聊机器人"),

    VIRTUAL_BOT(4, "企微虚拟号");

    public final Integer code;
    public final String name;

    ApplicationDataSourceEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public static ApplicationDataSourceEnum formCode(Integer code) {
        for (ApplicationDataSourceEnum value : ApplicationDataSourceEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        throw new IllegalArgumentException("ApplicationDataSourceEnum.formCode: type=" + code);
    }
}
