package com.ke.chat.tag.api.enums;

/**
 * <AUTHOR>
 * @date 2023/3/1 11:11 上午
 **/
public enum TagTaskTypeEnum {
    TAG_SINGLE_CHOICE_SCORE((byte) 3, "数据标注时打分单选"),
    TAG_MULTI_CHOICE_SCORE((byte) 2, "数据标注时打分多选"),
    CHAT_THUMBS_UP_DOWN((byte) 1, "聊天时点赞点踩"),
    TAG_SORT((byte) 4, "数据标注时排序"),
    SUPPLEMENT_ANSWER((byte) 5, "补充答案"),
    TEXT_IMAGE_MARK((byte) 6, "文图标注"),
    IMAGE_MARK((byte) 7, "图片标注"),
    IMAGE_SCORE_MARK((byte) 8, "图片打分任务"),
    IMAGE_TEXT_MARK((byte) 9, "图文标注");


    public final Byte code;
    public final String message;

    TagTaskTypeEnum(Byte code, String message) {
        this.code = code;
        this.message = message;
    }

    public Byte getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }

    public static TagTaskTypeEnum valueOf(Byte type) {
        for (TagTaskTypeEnum typeEnum : values()) {
            if (typeEnum.code.equals(type)) {
                return typeEnum;
            }
        }
        return null;
    }
}
