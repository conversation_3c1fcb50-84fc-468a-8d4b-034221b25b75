package com.ke.chat.tag.api.util;

import com.ke.risk.safety.common.s3.util.S3Service;
import com.ke.risk.safety.common.util.UUIDUtil;
import com.ke.risk.safety.common.util.date.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.DigestUtils;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.util.UriComponentsBuilder;

import java.io.IOException;
import java.io.InputStream;
import java.io.UnsupportedEncodingException;
import java.net.URI;
import java.net.URLDecoder;

@Service
@Slf4j
public class S3OuterService extends S3Service {

    @Value("${chat.tag.ak}")
    private String accessKey;
    @Value("${chat.tag.sk}")
    private String secretKey;
    @Value("${s3.endPoint.outer}")
    private String endPoint;
    @Value("${s3.endPoint.fileOuter}")
    private String fileEndPoint;
    @Value("${s3.endPoint.inner}")
    private String innerPoint;
    @Value("${s3.default.expireSeconds}")
    private Long expireSeconds;
    @Value("${s3.default.bucketName}")
    private String bucketName;

    @Override
    public String getAccessKey() {
        return this.accessKey;
    }

    @Override
    public String getSecretKey() {
        return this.secretKey;
    }

    @Override
    public String getEndpoint() {
        return this.endPoint;
    }

    private static final String UPLOAD_S3_PREFIX = "chat_tag_picture";

    public Boolean putObjectFromPrefix(String bucketName, String randomFileName, InputStream inputStream, String type) {
        try {
            uploadOnBlocked(bucketName, randomFileName, inputStream, null);
            //uploadOnBlocked(bucketName, randomFileName, inputStream, "multipart/form-data");
        } catch (Exception e) {
            LOGGER.error("s3 putObjectFromPrefix is exception, bucketName:{}, randomFileName:{}, e:{}",
                bucketName, randomFileName, e);
            return Boolean.FALSE;
        } finally {
            if (null != inputStream) {
                try {
                    inputStream.close();
                } catch (Exception e) {
                    LOGGER.error("S3InnerService putObjectFromPrefix close inputStream Exception, bucketName:{}, randomFileName:{},  e:{}",
                        bucketName, randomFileName, e);
                }
            }
        }

        return Boolean.TRUE;
    }

    public String generateFileName(String fileType) {
        return new StringBuffer(UPLOAD_S3_PREFIX)
            .append(DateUtil.getNow(DateUtil.NUMBER_FORMAT))
            .append(UUIDUtil.getUUID32())
            .append(".")
            .append(fileType).toString();
    }

    public String upload2S3(MultipartFile file) throws IOException {
        String type = StringUtils.substringAfterLast(file.getResource().getFilename(), ".");
        String fileName = generateFileName(type);
        if (putObjectFromPrefix(bucketName, fileName, file.getInputStream(), type)) {
            return generatePresignedUrl(bucketName, fileName, expireSeconds);
        }
        return null;
    }

    public void uploadOnBlocked(String fileName, InputStream is, String contentType) {
        uploadOnBlocked(bucketName, fileName, is, contentType);
    }

    public String generatePresignedUrl(String objectKey) {
        return generatePresignedUrl(bucketName, objectKey, expireSeconds);
    }

    /**
     * 加签
     *
     * @param url
     * @return
     * @throws UnsupportedEncodingException
     */
    public String getSignedImgUrl(String url) {

        if (StringUtils.isEmpty(url)) {
            return url;
        }

        if (url.startsWith("http:")) {
            url = url.replaceFirst("http", "https");
        }

        URI uri = UriComponentsBuilder.fromUriString(url.replace(innerPoint, endPoint)).build().encode().toUri();
        return getSignUrl(uri, expireSeconds);
    }

    /**
     * 加签
     *
     * @param url
     * @return
     * @throws UnsupportedEncodingException
     */
    public String getSignedImgUrlWithGivenTime(String url, long startTime, long expireSeconds) {

        if (StringUtils.isEmpty(url)) {
            return url;
        }

        if (url.startsWith("http:")) {
            url = url.replaceFirst("http", "https");
        }

        URI uri = UriComponentsBuilder.fromUriString(url.replace(innerPoint, endPoint)).build().encode().toUri();
        return getSignUrlWithGivenEndTime(uri, startTime, expireSeconds);
    }

    public String getSignedImgUrlWithTime(String url, long expireTime) {

        if (StringUtils.isEmpty(url)) {
            return url;
        }

        if (url.startsWith("http:")) {
            url = url.replaceFirst("http", "https");
        }

        URI uri = UriComponentsBuilder.fromUriString(url.replace(innerPoint, endPoint)).build().encode().toUri();
        return getSignUrl(uri, expireTime);
    }

    public String getSignedFileUrl(String url) {
        return getSignedFileUrl(url, expireSeconds);
    }

    public String getSignedFileUrl(String url, Long expireSeconds) {

        if (StringUtils.isEmpty(url)) {
            return url;
        }

        if (url.startsWith("http:")) {
            url = url.replaceFirst("http", "https");
        }
        URI uri = UriComponentsBuilder.fromUriString(url.replace(innerPoint, fileEndPoint)).build().encode().toUri();
        return getSignUrl(uri, expireSeconds);
    }


    /**
     * 加签
     *
     * @param fileKey
     * @return
     * @throws UnsupportedEncodingException
     */
    public String getFileSignedUrlByFileKey(String fileKey) {

        if (StringUtils.isEmpty(fileKey)) {
            return fileKey;
        }
        String url = generatePresignedUrl(fileKey);
        URI uri = UriComponentsBuilder.fromUriString(url.replace(endPoint, fileEndPoint)).build().encode().toUri();
        return getSignUrl(uri, expireSeconds);
    }

    /**
     * Image加签
     *
     * @param fileKey
     * @return
     * @throws UnsupportedEncodingException
     */
    public String getImageSignedUrlByFileKey(String fileKey) {

        if (StringUtils.isEmpty(fileKey)) {
            return fileKey;
        }
        String url = generatePresignedUrl(fileKey);
        URI uri = UriComponentsBuilder.fromUriString(url.replace(endPoint, endPoint)).build().encode().toUri();
        return getSignUrl(uri, expireSeconds);
    }

    @NotNull
    private String getSignUrl(URI uri, long expireTime) {
        String host = uri.getHost();
        String path = uri.getPath();
        String decodePath;
        try {
            decodePath = URLDecoder.decode(path, "UTF-8");
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
            decodePath = path;
        }

        long time = System.currentTimeMillis() / 1000;
        String signBuilder = "ak=" + accessKey +
            "&exp=" + expireTime +
            "&path=" + decodePath +
            "&ts=" + time +
            "&sk=" + secretKey;
        String sign = DigestUtils.md5DigestAsHex(signBuilder.getBytes());

        String params = "ak=" + accessKey +
            "&exp=" + expireTime +
            "&ts=" + time +
            "&sign=" + sign;

        return uri.getScheme() + "://" + host + path + "?" + params;
    }

    @NotNull
    private String getSignUrlWithGivenEndTime(URI uri, long startTime, long expireTime) {
        String host = uri.getHost();
        String path = uri.getPath();
        String decodePath;
        try {
            decodePath = URLDecoder.decode(path, "UTF-8");
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
            decodePath = path;
        }

        long time = System.currentTimeMillis() / 1000;
        String signBuilder = "ak=" + accessKey +
            "&exp=" + expireTime +
            "&path=" + decodePath +
            "&ts=" + startTime +
            "&sk=" + secretKey;
        String sign = DigestUtils.md5DigestAsHex(signBuilder.getBytes());

        String params = "ak=" + accessKey +
            "&exp=" + expireTime +
            "&ts=" + startTime +
            "&sign=" + sign;

        return uri.getScheme() + "://" + host + path + "?" + params;
    }
}
