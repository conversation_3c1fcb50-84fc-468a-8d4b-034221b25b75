package com.ke.chat.tag.api.dto.application;

import com.ke.chat.tag.api.response.BasePageRequest;
import com.ke.chat.tag.api.response.BasePageResponse;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.List;

/**
 * function:
 *
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ApplicationSimpleInfoRespDTO extends BasePageResponse {

    private List<ApplicationSimpleInfo> applications;

    private Boolean hasMore;

    public ApplicationSimpleInfoRespDTO(BasePageRequest request, Integer total) {
        super(request, total);
    }

    @Data
    @SuperBuilder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class ApplicationSimpleInfo{

        private Long applicationId;

        private String name;

        private String icon;
    }
}
