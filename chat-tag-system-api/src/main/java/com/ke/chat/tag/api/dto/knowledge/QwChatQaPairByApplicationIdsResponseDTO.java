package com.ke.chat.tag.api.dto.knowledge;

import com.ke.chat.tag.api.response.BasePageResponse;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

@Data
@Builder
@Slf4j
@NoArgsConstructor
@AllArgsConstructor
public class QwChatQaPairByApplicationIdsResponseDTO extends BasePageResponse {
    private static final long serialVersionUID = 1L;
    private List<QwChatQaPairRecordByApplicationIds> qaList;

    public QwChatQaPairByApplicationIdsResponseDTO(Integer pageNum, Integer pageSize, Integer total) {
        super(pageSize, pageNum, total);
    }
}
