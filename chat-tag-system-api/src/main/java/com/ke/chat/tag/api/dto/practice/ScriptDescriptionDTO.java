package com.ke.chat.tag.api.dto.practice;

import com.ke.boot.common.exception.BusinessException;
import com.ke.chat.tag.api.dto.practice.challenge.ChallengeDetailCheck;
import com.ke.chat.tag.api.exception.ErrorCode;
import org.apache.commons.lang3.StringUtils;

import java.util.LinkedHashMap;
import java.util.Objects;

public class ScriptDescriptionDTO extends LinkedHashMap<String, String> implements ChallengeDetailCheck {

    @Override
    public void checkField() {
        if (isEmpty()) {
            throw new BusinessException(ErrorCode.INVALID_ARGUMENT.code, "脚本描述中必须包含任务目标");
        }
        String firstKey = keySet().stream().findFirst().orElse("");
        if (StringUtils.isBlank(firstKey) || !Objects.equals(firstKey, "任务目标") || StringUtils.isBlank(get(firstKey))) {
            throw new BusinessException(ErrorCode.INVALID_ARGUMENT.code, "脚本描述中必须包含任务目标");
        }
    }
}
