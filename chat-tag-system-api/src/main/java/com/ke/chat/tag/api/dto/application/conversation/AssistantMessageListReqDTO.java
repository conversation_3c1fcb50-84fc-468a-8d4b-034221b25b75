package com.ke.chat.tag.api.dto.application.conversation;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.annotation.Nullable;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class AssistantMessageListReqDTO {

    /**
     * 消息cursor，默认按照消息从新往前查，即倒序查
     * 如果为空，表示是第一页
     * 如果不为空，则从当前cursor开始往前查询
     */
    @Nullable
    private String messageId;

}
