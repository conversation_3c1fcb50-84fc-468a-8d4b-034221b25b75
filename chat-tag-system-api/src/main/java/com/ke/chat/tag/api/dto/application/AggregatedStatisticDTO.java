package com.ke.chat.tag.api.dto.application;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class AggregatedStatisticDTO {

    @JsonFormat(pattern = "MM-dd")
    private LocalDateTime time;

    private Long interaction;

    private Long uv;

    private Long likes;
}
