package com.ke.chat.tag.api.dto.application;

import com.ke.chat.tag.api.dto.knowledge.KnowledgeFileResponseDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
public class SseAskQuestionRespDTO {

    private String type;

    /**
     * 消息id
     */
    private String answerId;

    /**
     * 下标
     * 用于表示当前数据包“消息正文 content”在整个正文的索引
     */
    private Integer index;

    /**
     * 消息正文
     */
    private Object content;


    private Integer citeKnowledgeNum;
    /**
     * 相关知识
     */
    private List<KnowledgeFileResponseDTO> knowledge;

    /**
     * 记录中的图片
     */
    private List<String> images;

    private Object documents;

    /**
     * 当出现异常时，讲异常全部透传
     */
    private OpenAiError error;

    private Long creatTime;

}
