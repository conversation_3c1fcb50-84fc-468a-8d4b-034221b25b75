package com.ke.chat.tag.api.enums;

import lombok.Getter;

@Getter
public enum ApplicationStatisticalDataTypeEnum {
    UV(1, "用户量UV"),

    PV(2, "互动量PV"),
    LIKES_DISLIKES_TOTAL_NUM(3, "赞踩总量"),
    LIKES_DISLIKES_RATIO(4, "赞踩占比"),
    LIKES_NUM(5, "点赞量"),
    DISLIKES_NUM(6, "点踩量"),
    LIKES_RATIO(7, "点赞率"),
    DISLIKES_RATIO(8, "点踩率"),
    NEXT_DAY_RETENTION(9, "次日留存率"),
    SEVEN_DAY_RETENTION(10, "7日留存率"),
    THIRTY_DAY_RETENTION(11, "30日留存率");

    public final Integer code;
    public final String name;

    ApplicationStatisticalDataTypeEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }
}
