package com.ke.chat.tag.api.dto.application;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ApplicationLaunchReqDTO {

    @NotNull(message = "应用id不能为空")
    private Long applicationId;

    @NotNull(message = "操作不能为空")
    private Boolean launch;
}
