package com.ke.chat.tag.api.enums.practice;

import lombok.Getter;

@Getter
public enum ChallengeModeEnum {
    /**
     * 角色对话 - 与特定角色进行的对话
     */
    CHARACTER_DIALOGUE(0,"角色对话"),

    /**
     * 知识问答 - 基于知识的问答交互
     */
    KNOWLEDGE_QA(1, "知识问答");

    ChallengeModeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    private int code;
    private String desc;

    public static String getDescByCode(int code) {
        for (ChallengeModeEnum mode : ChallengeModeEnum.values()) {
            if (mode.getCode() == code) {
                return mode.getDesc();
            }
        }
        return "";
    }
}
