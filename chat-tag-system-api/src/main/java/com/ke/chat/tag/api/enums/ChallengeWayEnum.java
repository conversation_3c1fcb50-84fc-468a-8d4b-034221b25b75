package com.ke.chat.tag.api.enums;


import com.ke.chat.tag.api.dto.practice.ChallengeButtonDTO;
import lombok.Getter;
import software.amazon.awssdk.utils.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: yumiao05
 * @Date: 2025/04/18/14:41
 * @Description:
 */
@Getter
public enum ChallengeWayEnum {

    AUDIO("1", "我要挑战"),

    TEXT("2", "文字挑战"),

    TEXT_AND_AUDIO("3","文字+语音");

    private final String code;

    private final String buttonName;

    ChallengeWayEnum(String code, String buttonName){
        this.code = code;
        this.buttonName = buttonName;

    }
    public static List<ChallengeButtonDTO>  getButtonListByCode(String code){
        List<ChallengeButtonDTO> res = new ArrayList<>();
        if (StringUtils.equals(code, TEXT_AND_AUDIO.code)) {
            ChallengeButtonDTO button1 = new ChallengeButtonDTO();
            button1.setType(ChallengeWayEnum.TEXT.getCode());
            button1.setName(ChallengeWayEnum.TEXT.getButtonName());
            ChallengeButtonDTO button2 = new ChallengeButtonDTO();
            button2.setType(ChallengeWayEnum.AUDIO.getCode());
            button2.setName(ChallengeWayEnum.AUDIO.getButtonName());
            res.add(button1);
            res.add(button2);
        }else{
            for(ChallengeWayEnum challengeWay : ChallengeWayEnum.values()){
                if(Objects.equals(challengeWay.getCode(), code)){
                    ChallengeButtonDTO button = new ChallengeButtonDTO();
                    button.setType(challengeWay.getCode());
                    button.setName(challengeWay.getButtonName());
                    res.add(button);
                }
            }
        }
        return res;
    }

}
