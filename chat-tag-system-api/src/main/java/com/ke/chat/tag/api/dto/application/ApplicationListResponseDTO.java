package com.ke.chat.tag.api.dto.application;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ApplicationListResponseDTO {

    public final static int TYPE_BELLA = 1;
    public final static int TYPE_WEB = 2;

    public final static String SOURCE_PLATFORM = "platform";
    public final static String SOURCE_USER = "user";

    private boolean hasMore;
    private List<ApplicationDetail> apps;

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class ApplicationDetail {

        /**
         * 应用类型，1-Bella应用；2-web应用
         */
        private Integer appType;

        /**
         * 应用跳转url，当type为2时有意义；
         */
        private String appUrl;

        /**
         *
         */
        private Long applicationId;

        /**
         * 来源，平台内置或个人
         */
        private String source;

        /**
         * 应用name
         */
        private String name;

        /**
         * 应用描述
         */
        private String description;

        /**
         * 应用icon
         */
        private String icon;

        /**
         * 应用类型 knowledge: 知识库；role：角色
         */
        private String type;

        /**
         * 点赞数
         * 注：webapp（即广告位应用）暂不支持
         */
        private Long likes;

        /**
         * 热度
         * 注：webapp（即广告位应用）暂不支持
         */
        private String heat;

        /**
         * 后端排序用
         * 热度
         * 注：webapp（即广告位应用）暂不支持
         */
        private Long heatTemp;

        /**
         * 本人是否点赞
         * 注：webapp（即广告位应用）暂不支持
         */
        private boolean liked;

        /**
         * 该用户是否可编辑
         */
        private boolean editable;

        /**
         * 用户量
         */
        private Long uv;

        /**
         * 创建时间
         */
        private LocalDateTime createTime;

        /**
         * 创建时间
         */
        private LocalDateTime exposureTime;

        /**
         * 创建用户昵称
         */
        private String nickName;

        /**
         * 收藏数
         * 注：webapp（即广告位应用）暂不支持
         */
        private Integer collects;

        /**
         * 本人是否收藏
         * 注：webapp（即广告位应用）暂不支持
         */
        private boolean collected;

        /**
         * 标签
         */
        private List<String> tags;

        /**
         * 用户所属部门
         */
        private String orgName;

        /**
         * 可复制智能体标签
         */
        private List<String> enableCopyAppTags;
        /**
         * 智能体复制次数
         */
        private Long copyCount;
        /**
         * 智能体模式
         */
        private String mode;
    }

    public static ApplicationDetail create(ApplicationInfo applicationInfo) {

        return ApplicationDetail.builder()
            .appType(TYPE_BELLA)
            .appUrl("")
            .applicationId(applicationInfo.getApplicationId())
            .source(applicationInfo.getSource())
            .name(applicationInfo.getName())
            .description(applicationInfo.getDescription())
            .icon(applicationInfo.getIcon())
            .type(applicationInfo.getType())
            .likes(applicationInfo.getLikes())
            .heat(applicationInfo.getHeat())
            .heatTemp(applicationInfo.getHeatTemp())
            .liked(applicationInfo.isLiked())
            .editable(applicationInfo.isEditable())
            .uv(applicationInfo.getUv())
            .createTime(applicationInfo.getCreateTime())
            .exposureTime(applicationInfo.getExposureTime())
            .nickName(applicationInfo.getNickName())
            .collects(applicationInfo.getCollects())
            .collected(applicationInfo.isCollected())
            .orgName(applicationInfo.getOrgName())
            .tags(applicationInfo.getTags())
            .build();
    }

    public static ApplicationDetail create(WebApplicationInfo webApplicationInfo) {

        return ApplicationDetail.builder()
            .appType(TYPE_WEB)
            .appUrl(webApplicationInfo.getAppUrl())
            .applicationId(0L)
            .source(SOURCE_PLATFORM)
            .name(webApplicationInfo.getAppName())
            .description(webApplicationInfo.getDesc())
            .icon(webApplicationInfo.getImgUrl())
            .type("")
            .likes(0L)
            .heat("")
            .heatTemp(0L)
            .liked(false)
            .editable(false)
            .uv(0L)
            .createTime(null)
            .exposureTime(null)
            .nickName(null)
            .collects(0)
            .collected(false)
            .build();
    }

}
