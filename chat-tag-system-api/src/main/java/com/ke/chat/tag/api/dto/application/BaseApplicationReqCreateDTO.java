package com.ke.chat.tag.api.dto.application;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class BaseApplicationReqCreateDTO {

    /**
     * 应用名称
     */
    @NotEmpty(message = "应用名称不能为空")
    @Size(max = 10, message = "应用名称不超过10个字")
    private String name;

    /**
     * 应用介绍
     */
    @NotEmpty(message = "应用介绍不能为空")
    @Size(max = 100, message = "应用介绍不超过100个字")
    private String description;

    /**
     * 图标url
     */
    @NotEmpty(message = "应用图标不能为空")
    private String icon;

    /**
     * 模型
     */
    @NotEmpty(message = "应用模型不能为空")
    private String model;

    /**
     * 应用标签
     */
    @Size(max = 3)
    private List<String> tags;

    /**
     * 是否开启欢迎词
     * true: 开启；false: 关闭
     */
    private Boolean welcomeWordsEnabled;

    /**
     * 欢迎词
     */
    private String welcomeWords;

    /**
     * 示例提问集 ListJson结构
     */
    private List<String> welcomeWordsExampleQuestions;

    /**
     * 角色介绍
     */
    private String roleInstruction;

    /**
     * 是否开启提示词
     * true：开启；false：关闭
     */
    private Boolean prompterEnabled;

    /**
     * 提示词
     */
    private List<String> prompts;

    /**
     * 可见性 public:公开；private：私有
     */
    private String visibility;

    /**
     * 应用类型
     */
    private String type;

    /**
     * 知识id
     * 只有当type= "knowledge"时非空
     */
    private List<String> fileIds;

    /**
     * 创建者昵称
     */
    private String nickName;

    /**
     * 检索配置
     */
    private SearchParam searchParam;

    /**
     * 协作组名
     */
    private String groupName;


}
