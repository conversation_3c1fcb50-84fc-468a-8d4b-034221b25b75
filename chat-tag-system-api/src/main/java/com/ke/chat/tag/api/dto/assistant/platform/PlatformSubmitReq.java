package com.ke.chat.tag.api.dto.assistant.platform;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class PlatformSubmitReq {

    @NotEmpty
    private String platformId;

    @NotNull
    private Long applicationId;
}
