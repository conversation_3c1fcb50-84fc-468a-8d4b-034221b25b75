package com.ke.chat.tag.api.enums;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2023/7/7 5:13 下午
 **/
public enum KnowledgeFileStatusEnum {

    UPLOAD_SUCCESS("upload_success", "上传成功"),

    EMBEDDING_SUCCESS("embedding_success", "向量化成功"),
    EMBEDDING_FAILED("embedding_failed", "向量化失败"),

    PARSED_SUCCESS("parse_success", "解析成功"),

    IN_PROGRESS("in_progress", "解析中"),

    FAILED("failed", "解析失败"),

    ASYNC_DELETE("async_delete", "删除中"),

    DELETE("deleted", "已删除"),

    DELETE_FAILED("delete_failed", "删除失败");

    public final String code;
    public final String desc;
    private static Map<String, KnowledgeFileStatusEnum> map = new HashMap<>();

    static {
        for (KnowledgeFileStatusEnum sourceEnum : KnowledgeFileStatusEnum.values()) {
            map.put(sourceEnum.code, sourceEnum);
        }
    }

    KnowledgeFileStatusEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static Optional<KnowledgeFileStatusEnum> getByCode(String code) {
        return Optional.ofNullable(map.get(code));
    }
}
