package com.ke.chat.tag.api.util;

import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.ke.chat.tag.api.bean.rss.RSSResult;
import com.ke.risk.safety.common.util.date.DateUtil;
import com.thoughtworks.xstream.XStream;
import com.thoughtworks.xstream.io.xml.DomDriver;
import lombok.extern.slf4j.Slf4j;

import java.net.Proxy;
import java.util.List;

@Slf4j
public class XmlUtil {

    private static final XStream instance = new XStream(new DomDriver("UTF-8"));

    /**
     * 添加合法的包名
     */
    private static final String PACKET = "com.ke.**";

    public static RSSResult rssResult(String rssUrl, Proxy proxy, String credential) {

        LOGGER.info("rssResult() rssUrl:{}", rssUrl);
        String content = HttpUtil.create().initClient(builder -> {

            if (proxy != null) {
                LOGGER.info("rssResult() rssUrl:{} add proxy!", rssUrl);
                builder.proxy(proxy);
            }

            if (!Strings.isNullOrEmpty(credential)) {
                LOGGER.info("rssResult() rssUrl:{} add credential!", rssUrl);
                builder.proxyAuthenticator((route, response) -> response.request().newBuilder().header("Proxy-Authorization", credential).build());
            }
        }).doGet(rssUrl);
        if (Strings.isNullOrEmpty(content)) {
            LOGGER.info("rssResult() content is null or empty！ rssUrl:{}", rssUrl);
            return null;
        }
        instance.allowTypesByWildcard(new String[]{PACKET});
        instance.ignoreUnknownElements();
        instance.processAnnotations(RSSResult.class);

        try {
            return (RSSResult) instance.fromXML(content);
        } catch (Exception e) {
            LOGGER.error("rssResult()", e);
        }
        return null;
    }

    public static void main(String[] args) {
//		List<String> urls = Lists.newArrayList(
//			"https://rsshubfortest.vercel.app/36kr/motif/327686782977",
//			"https://rsshubfortest.vercel.app/theverge/ai-artificial-intelligence"
//		);
//		List<RSSResult> results = Lists.newArrayList();
//		for (String url : urls) {
//			RSSResult rssResult = rssResult(url);
//			results.add(rssResult);
//		}

        List<String> dateStrs = Lists.newArrayList(
            "Tue, 21 Nov 2023 23:33:50 GMT",
            "Tue, 6 Nov 2023 23:33:50 GMT"
        );
        String srcFormat = "EEE, d MMM yyyy HH:mm:ss 'GMT'";
        String dstFormat = "yyyy-MM-dd HH:mm:ss";
        List<String> logdayList = Lists.newArrayList();
        for (String dateStr : dateStrs) {
            String logday = DateUtil.exchange(dateStr, srcFormat, dstFormat);
            logdayList.add(logday);
        }
        System.out.println("OK");
    }

}
