package com.ke.chat.tag.api.dto.audio.event.server;

import com.ke.chat.tag.api.dto.audio.event.BaseEvent;
import lombok.Builder;
import lombok.Data;
import lombok.Getter;

import static com.ke.chat.tag.api.enums.audio.EventEnum.INPUT_AUDIO_TRANSCRIPT_DONE;

/**
 * 当客户端输入的音频数据asr转换完成时，或发生中断，取消时响应该事件到客户端
 */
@Data
@Builder
public class InputAudioTranscriptDone extends BaseEvent {


    public final String type = INPUT_AUDIO_TRANSCRIPT_DONE.type;

    private String itemId;

    private String transcript;
}
