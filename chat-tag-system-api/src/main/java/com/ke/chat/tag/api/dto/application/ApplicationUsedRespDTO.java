package com.ke.chat.tag.api.dto.application;

import com.ke.chat.tag.api.response.BasePageResponse;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * function:我用过的的智能体返回结果
 *
 * <AUTHOR>
 */
@Data
public class ApplicationUsedRespDTO extends BasePageResponse {

    /**
     * 用户的智能体明细数据
     */
    private List<ApplicationUsedRespDetail> usedList;


    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ApplicationUsedRespDetail{

        /**
         * 智能体id(外部id)
         */
        private Long applicationId;

        /**
         * 智能体名称
         */
        private String name;

        /**
         * 智能体描述
         */
        private String description;

        /**
         * icon 链接
         */
        private String icon;

        /**
         * 智能体热度值描述
         */
        private String heat;

        /**
         * 智能体使用的模型名称
         */
        private String modelName;

        /**
         * 智能体支持最大输入字符数
         */
        private Integer inputLimit;

    }
}
