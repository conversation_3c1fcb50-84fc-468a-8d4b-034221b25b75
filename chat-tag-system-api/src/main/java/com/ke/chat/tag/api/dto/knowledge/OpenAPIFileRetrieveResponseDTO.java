package com.ke.chat.tag.api.dto.knowledge;

import lombok.Builder;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

@Slf4j
@Data
@Builder
public class OpenAPIFileRetrieveResponseDTO {

    private String id;
    private long created_at;
    private String object;
    private String status;
    private List<RetrieveResponseDTO> list;

    @Data
    @Builder
    @Slf4j
    public static class RetrieveResponseDTO {
        private String id;
        private String file_id;
        private String file_name;
        private Float score;
        private String chunk_id;
        private String content;
        private String file_tag;
    }

}
