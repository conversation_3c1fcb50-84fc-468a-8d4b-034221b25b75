package com.ke.chat.tag.api.util;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Lists;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.JsonSyntaxException;
import com.google.gson.reflect.TypeToken;
import com.ke.boot.common.exception.BusinessException;
import lombok.Getter;
import lombok.Setter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.lang.reflect.Array;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 *
 */

public class JsonUtil {
    private static final Logger logger = LoggerFactory.getLogger(JsonUtil.class);

    private static final ObjectMapper objectMapper = new ObjectMapper();

    @Setter
    @Getter
    public static class TypeBean {
        private Class c;
        private List<TypeBean> list;

        public TypeBean(Class c) {
            this.c = c;
            this.list = Lists.newArrayList();
        }

        public TypeBean addSubTypeBean(TypeBean tb) {
            list.add(tb);
            return this;
        }

        public Type gType() {

            if (list == null || list.size() == 0) {
                return c;
            }

            List<Type> ts = Lists.newArrayList();
            for (TypeBean t : list) {
                ts.add(t.gType());
            }
            return type(c, ts);
        }
    }

    /**
     * serializeNulls(): gson 默认会对为null的字段，在toJson()时去掉。添加这个配置之后，也会输出，类似：{"A":null}
     *
     * @return
     */
    private static Gson gson() {
        return new GsonBuilder()
            .serializeNulls()
            .create();
    }

    /**
     * parse String to Bean
     * T内部没有泛型
     */
    public static <T> T parseStr2Bean(String jsonStr, final Class<T> c) {
        try {
            return gson().fromJson(jsonStr, c);
        } catch (Exception e) {
            logger.warn("JsonUtil parseStr2Bean(无泛型) exception", e);
            return null;
        }
    }

    /**
     * parse String to Bean
     * T内部有泛型，且只有一层，即T内部字段的类型不会再有泛型
     *
     * @param ts T内部一层的泛型
     */
    public static <T> T parseStr2Bean(String jsonStr, final Class<T> c, final Type... ts) {
        try {
            return gson().fromJson(jsonStr, type(c, ts));
        } catch (Exception e) {
            logger.warn("JsonUtil parseStr2Bean(一层泛型) exception", e);
            return null;
        }
    }

    /**
     * parse String to Bean
     * T内部有泛型，不限层级
     *
     * @param tb T内部泛型层级结构
     */
    public static <T> T parseStr2Bean(String jsonStr, final TypeBean tb) {
        try {
            return gson().fromJson(jsonStr, tb.gType());
        } catch (Exception e) {
            logger.warn("JsonUtil parseStr2Bean(多层泛型) exception", e);
            return null;
        }
    }

    /**
     * parse Bean to String
     * T内部没有泛型
     */
    public static <T> String parseBean2Str(T t) {
        if (t == null) {
            return "";
        }
        return gson().toJson(t);
    }

    /**
     * parse Bean to String
     * T内部有泛型，且只有一层，即T内部字段的类型不会再有泛型
     *
     * @param ts T内部一层的泛型
     */
    public static <T> String parseBean2Str(T t, final Class<T> c, final Type... ts) {
        if (t == null) {
            return "";
        }
        return gson().toJson(t, type(c, ts));
    }

    /**
     * parse Bean to String
     * T内部有泛型，不限层级
     *
     * @param tb T内部泛型层级结构
     */
    public static <T> String parseBean2Str(T t, final TypeBean tb) {
        if (t == null) {
            return "";
        }
        return gson().toJson(t, tb.gType());
    }

    /**
     * parse to List<Bean>
     * 单个写法：gson().fromJson(jsonStr, new TypeToken<List<ChengDuBean>>(){}.getType())
     */
    public static <T> List<T> parseStr2List(String json, Class<T> c1) {
        try {
            return gson().fromJson(json, type(List.class, c1));
        } catch (Exception e) {
            logger.warn("JsonUtil parseStr2List exception", e);
            return null;
        }
    }

    /**
     * 使用objectMapper 实现parseStr2List 功能
     */
    public static <T> List<T> parseStr2ListByObjectMapper(String json, Class<T> c1) {
        try {
            return objectMapper.readValue(json, objectMapper.getTypeFactory().constructCollectionType(List.class, c1));
        } catch (Exception e) {
            logger.warn("JsonUtil parseStr2ListByObjectMapper exception", e);
            return null;
        }
    }

    /**
     * 用法：
     * gson().fromJson(jsonStr, JsonUtil.type(BaseBean.class, c));
     * gson().toJson(this, JsonUtil.type(BaseBean.class, c));
     */
    public static ParameterizedType type(final Class raw, final List<Type> ts) {
        if (ts == null || ts.size() == 0) {
            return type(raw, new Type[0]);
        } else {
            return type(raw, ts.toArray((Type[]) Array.newInstance(Type.class, ts.size())));
        }
    }

    public static ParameterizedType type(final Class raw, final Type... ts) {
        return new ParameterizedType() {
            public Type getRawType() {
                return raw;
            }

            public Type[] getActualTypeArguments() {
                return ts;
            }

            public Type getOwnerType() {
                return null;
            }
        };
    }

    public static <K, V> Map<K, V> jsonToMap(String json, Class<K> clazzK, Class<V> clazzV) {
        if (json == null) {
            return null;
        } else {
            //反序列化map
            JavaType javaType = objectMapper.getTypeFactory().constructParametricType(HashMap.class, clazzK, clazzV);
            try {
                return objectMapper.readValue(json, javaType);
            } catch (Exception e) {
                throw new BusinessException("500", "Unable to parse Json String.");
            }
        }
    }

    /**
     * 使用objectMapper将对象转成json
     */
    public static String objectToJson(Object object) {
        try {
            return objectMapper.writeValueAsString(object);
        } catch (JsonProcessingException e) {
            throw new BusinessException("500", "Unable to parse Json String.");
        }
    }

    public static <T> T mapConvertToBean(Map<String, Object> map, Class<T> clazz) {
        try {
            T instance = clazz.getDeclaredConstructor().newInstance();
            Arrays.stream(clazz.getDeclaredFields()).forEach(field -> {
                field.setAccessible(true);
                Optional.ofNullable(map.get(field.getName()))
                    .ifPresent(value -> {
                        try {
                            field.set(instance, field.getType().equals(Map.class) && value instanceof String
                                ? objectMapper.readValue((String) value, Map.class) : value);
                        } catch (IllegalAccessException | JsonProcessingException e) {
                            logger.error("Error processing field: " + field.getName(), e);
                            throw new BusinessException("500", "Error processing field: " + field.getName(), e);
                        }
                    });
            });
            return instance;
        } catch (InstantiationException | IllegalAccessException | InvocationTargetException |
                 NoSuchMethodException e) {
            logger.error("Error creating instance of class: " + clazz.getName(), e);
            throw new BusinessException("500", "Error creating instance of class: " + clazz.getName(), e);
        }
    }

    /**
     * 将 JSON 格式的字符串转换为 List<String>。
     *
     * @param jsonString JSON 格式的字符串，例如：["item1","item2"]
     * @return 转换后的 List<String>，如果解析失败则返回空列表
     */
    public static List<String> jsonToList(String jsonString) {
        if (jsonString == null || jsonString.isEmpty()) {
            return Collections.emptyList();
        }

        try {
            Type listType = new TypeToken<List<String>>() {
            }.getType();
            return new Gson().fromJson(jsonString, listType);
        } catch (JsonSyntaxException e) {
            // 可以根据需要选择是否记录日志或重新抛出异常
            logger.error("JSON 解析错误jsonString:{} ", jsonString, e.getMessage());
            return Collections.emptyList();
        }
    }


    public static <T> T Json2ReferencObject(String json, TypeReference<T> typeReference) {
        try {
            return objectMapper.readValue(json, typeReference);
        } catch (IOException e) {
            logger.warn("json转换异常,传入的json为：{}", json, e);
            throw new BusinessException("400", "json转换异常");
        }
    }

}
