package com.ke.chat.tag.api.enums;

import lombok.Getter;

import java.util.Arrays;

/**
 * bella平台所有llm调用功能点
 */
@Getter
public enum LlmFunctionNodeEnum {

    AGENT("application", "agent", "智能体", true),
    GPT_BUILDER("platform", "gptBuilder", "智能体构建工具", true),
    KNOWLEDGE_RECYCLE("platform", "knowledgeRecycle", "知识回收（企微）", true),
    RECOMMEND_TAG("platform", "recommendTag", "推荐标签生成", false),
    DYNAMIC_PROMPT("platform", "dynamicPrompt", "动态提示词", false),
    WORKFLOW_DEBUG("platform", "workflowDebug", "工作流调试场", true),
    MAINFRAME_AGENT("platform", "mainframeAgent", "主框智能体", true),
    THREAD_SUMMARY("platform", "threadSummary", "会话总结", true),
    WORKFLOW_PROXY("platform", "workflowProxy", "工作流代理（ai营销使用）", true)
    ;


    LlmFunctionNodeEnum(String dimension, String name, String desc, boolean useChildAk) {
        this.dimension = dimension;
        this.name = name;
        this.desc = desc;
        this.useChildAk = useChildAk;
    }

    /**
     * 功能点维度：分为应用application及平台自带功能platform
     */
    private final String dimension;

    /**
     * 能力点名称
     */
    private final String name;

    /**
     * 能力点描述
     */
    private final String desc;

    /**
     * 是否分配子ak
     */
    private final boolean useChildAk;

    public static LlmFunctionNodeEnum getByName(String name) {
        return Arrays.stream(LlmFunctionNodeEnum.values())
            .filter(item -> item.getName().equals(name))
            .findFirst().orElse(null);
    }
}
