package com.ke.chat.tag.api.bean;

import com.ke.risk.safety.common.util.json.JsonUtils;
import lombok.Builder;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

/**
 * {
 * "name": "自研算法",
 * "key": "xxxx",
 * "desc": "自研算法",
 * "team": "质量保障中心-故障管理组",
 * "business": "知故",
 * "contacts": "秦玲玲"
 * }
 */
@Data
@Builder
@Slf4j
public class BellaApiBiz {

    private String name;
    private String key;
    private String desc;

    /**
     * 团队
     */
    private String team;

    /**
     * 业务名称
     */
    private String business;

    /**
     * 联系人
     */
    private String contacts;

    @Override
    public String toString() {
        return JsonUtils.parseBean2Str(this);
    }

}
