package com.ke.chat.tag.api.dto.knowledge;

import lombok.Builder;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

@Slf4j
@Data
@Builder
public class OpenAPIFileChunkResponseDTO {

    private String id;
    private long created_at;
    private String object;
    private List<ChunkResponseDTO> list;

    @Data
    @Builder
    @Slf4j
    public static class ChunkResponseDTO {
        private String chunk_id;
        private String chunk_tag;
        private Object content;
    }
}
