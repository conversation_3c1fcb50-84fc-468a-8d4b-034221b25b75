package com.ke.chat.tag.api.util;

import cn.hutool.core.util.IdUtil;
import com.ke.risk.safety.common.s3.util.S3Service;
import com.ke.risk.safety.common.util.UUIDUtil;
import com.ke.risk.safety.common.util.date.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.DigestUtils;
import org.springframework.web.multipart.MultipartFile;
import sun.net.www.protocol.http.Handler;

import java.io.BufferedOutputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.UnsupportedEncodingException;
import java.net.HttpURLConnection;
import java.net.URI;
import java.net.URL;
import java.net.URLDecoder;

@Service
@Slf4j
public class S3InnerService extends S3Service {
    @Value("${chat.tag.ak}")
    private String accessKey;
    @Value("${chat.tag.sk}")
    private String secretKey;
    @Value("${s3.endPoint.inner}")
    private String endPoint;
    @Value("${s3.default.expireSeconds}")
    private Long expireSeconds;
    @Value("${s3.default.bucketName}")
    private String bucketName;

    @Override
    public String getAccessKey() {
        return this.accessKey;
    }

    @Override
    public String getSecretKey() {
        return this.secretKey;
    }

    @Override
    public String getEndpoint() {
        return this.endPoint;
    }

    private static final String UPLOAD_S3_PREFIX = "chat_tag_picture";

    public Boolean putObjectFromPrefix(String bucketName, String randomFileName, InputStream inputStream, String type) {
        try {
            uploadOnBlocked(bucketName, randomFileName, inputStream, null);
            //uploadOnBlocked(bucketName, randomFileName, inputStream, "multipart/form-data");
        } catch (Exception e) {
            LOGGER.error("s3 putObjectFromPrefix is exception, bucketName:{}, randomFileName:{}, e:{}",
                bucketName, randomFileName, e);
            return Boolean.FALSE;
        } finally {
            if (null != inputStream) {
                try {
                    inputStream.close();
                } catch (Exception e) {
                    LOGGER.error("S3InnerService putObjectFromPrefix close inputStream Exception, bucketName:{}, randomFileName:{},  e:{}",
                        bucketName, randomFileName, e);
                }
            }
        }

        return Boolean.TRUE;
    }

    public String generateFileName(String fileType) {
        return new StringBuffer(UPLOAD_S3_PREFIX)
            .append(com.ke.risk.safety.common.util.date.DateUtil.getNow(DateUtil.NUMBER_FORMAT))
            .append(UUIDUtil.getUUID32())
            .append(".")
            .append(fileType).toString();
    }

    public String upload2S3(MultipartFile file) throws IOException {
        String type = StringUtils.substringAfterLast(file.getResource().getFilename(), ".");
        String fileName = generateFileName(type);
        if (putObjectFromPrefix(bucketName, fileName, file.getInputStream(), type)) {
            return fileName;
        }
        return null;
    }

    public void uploadOnBlocked(String fileName, InputStream is, String contentType) {
        uploadOnBlocked(bucketName, fileName, is, contentType);
    }

    public String generatePresignedUrl(String objectKey) {
        return generatePresignedUrl(bucketName, objectKey, expireSeconds);
    }

    /**
     * 加签
     *
     * @param url
     * @return
     * @throws UnsupportedEncodingException
     */
    public String getSignedUrl(String url) {

        if (StringUtils.isEmpty(url)) {
            return url;
        }

        URI uri = URI.create(url);
        String host = uri.getHost();
        String path = uri.getPath();
        String decodePath;
        try {
            decodePath = URLDecoder.decode(path, "UTF-8");
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
            decodePath = path;
        }

        long time = System.currentTimeMillis() / 1000;
        String signBuilder = "ak=" + accessKey +
            "&exp=" + expireSeconds +
            "&path=" + decodePath +
            "&ts=" + time +
            "&sk=" + secretKey;
        String sign = DigestUtils.md5DigestAsHex(signBuilder.getBytes());

        String params = "ak=" + accessKey +
            "&exp=" + expireSeconds +
            "&ts=" + time +
            "&sign=" + sign;

        return uri.getScheme() + "://" + host + path + "?" + params;
    }

    public InputStream getImageFromByUrl(String strUrl) {

        try {
            URL url = new URL(null, strUrl, new Handler());
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            connection.setConnectTimeout(10000);
            connection.setRequestMethod("GET");
            InputStream inputStream = connection.getInputStream();
            return inputStream;

        } catch (IOException e) {
            LOGGER.error("下载图片失败 ,图片url为:[{}]  errorMessage:[{}]"
                , strUrl, e);
        }
        return null;
    }

    public String upload2S3(InputStream inputStream, String type) throws IOException {

        String fileName = generateFileName(type);
        if (putObjectFromPrefix(bucketName, fileName, inputStream, type)) {
            return fileName;
        }
        return null;
    }

    public String upload2S3(MultipartFile file, String type) throws IOException {
        String fileName = generateFileName(type);
        if (putObjectFromPrefix(bucketName, fileName, file.getInputStream(), type)) {
            return fileName;
        }
        return null;
    }

    public File getPictureUrl(String base64) throws IOException {
        if (base64.contains("data:image")) {
            base64 = base64.substring(base64.indexOf(",") + 1);
        }
        base64 = base64.toString().replace("\r\n", "");
        //创建文件目录
        String prefix = ".jpg";
        File file = File.createTempFile(IdUtil.randomUUID(), prefix);
        BufferedOutputStream bos = null;
        FileOutputStream fos = null;
        try {
            // BASE64Decoder decoder = new BASE64Decoder();
            // byte[] bytes =  decoder.decodeBuffer(base64);
            byte[] bytes = Base64.decodeBase64(base64);
            fos = new FileOutputStream(file);
            bos = new BufferedOutputStream(fos);
            bos.write(bytes);
        } finally {
            if (bos != null) {
                try {
                    bos.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
            if (fos != null) {
                try {
                    fos.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        return file;
    }


}
