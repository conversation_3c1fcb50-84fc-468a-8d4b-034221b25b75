package com.ke.chat.tag.api.enums.practice;

import lombok.Getter;

@Getter
public enum ChallengePublishStatusEnum {

    /**
     * 下线
     */
    OFFLINE(-1, "已下线"),
    /**
     * 草稿
     */
    DRAFT(0, "草稿"),

    /**
     * 发布
     */
    PUBLISHED(1, "已上线"),
    ;

    private final int code;
    private final String desc;


    ChallengePublishStatusEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static String getDescByCode(int code) {
        for (ChallengePublishStatusEnum status : ChallengePublishStatusEnum.values()) {
            if (status.getCode() == code) {
                return status.getDesc();
            }
        }
        return "";
    }

    public static boolean isPublished(int code) {
        return PUBLISHED.getCode() == code;
    }
}
