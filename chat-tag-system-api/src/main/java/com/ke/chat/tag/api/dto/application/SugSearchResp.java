package com.ke.chat.tag.api.dto.application;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class SugSearchResp {

    private List<SuggestResult> suggestList;

    @Data
    public static class SuggestResult {
        private String suggest;
        private String keyWord;
    }
}
