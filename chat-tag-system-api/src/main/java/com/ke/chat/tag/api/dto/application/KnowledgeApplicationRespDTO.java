package com.ke.chat.tag.api.dto.application;

import com.ke.chat.tag.api.dto.knowledge.KnowledgeFileResponseDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class KnowledgeApplicationRespDTO extends BaseApplicationReqCreateDTO {

    /**
     * 应用id
     */
    private Long applicationId;

    private List<KnowledgeFileResponseDTO> knowledge;

}
