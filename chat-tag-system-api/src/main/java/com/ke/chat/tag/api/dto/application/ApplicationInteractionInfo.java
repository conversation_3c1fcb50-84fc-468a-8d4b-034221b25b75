package com.ke.chat.tag.api.dto.application;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ApplicationInteractionInfo {

    /**
     * 智能体外部id
     */
    private Long applicationId;

    /**
     * 智能体图标链接
     */
    private String icon;

    /**
     * 智能体名称
     */
    private String name;

    /**
     * 置顶状态，true表示置顶，非ture表示不置顶
     */
    private Boolean topping;

    /**
     * 是否是默认置顶，true表示是默认置顶，非true表示不是默认置顶
     */
    private Boolean defaultTop;

    /**
     * 是否是当前用户创建的智能体，ture表示是，非true表示不是
     */
    private Boolean personal;

    /**
     * 最后一次提问的问题
     */
    private String latestQuestion;

    private Boolean isRecent;

    /**
     * 最后一次提问时间，格式yyyy-MM-dd HH:mm:ss 示列：2024-08-13 17:46:12
     */
    private LocalDateTime lastAskTime;

    /**
     * 智能体热度描述
     */
    private String heat;

    /**
     * 智能体问答支持问题的最大输入字符数
     */
    private Integer inputLimit;

    /**
     * 智能体描述
     */
    private String description;
}
