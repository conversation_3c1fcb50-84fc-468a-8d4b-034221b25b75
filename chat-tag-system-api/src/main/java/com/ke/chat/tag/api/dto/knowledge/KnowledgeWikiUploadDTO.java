package com.ke.chat.tag.api.dto.knowledge;

import com.ke.chat.tag.api.validator.annotation.StringListElementLength;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.annotation.Nullable;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class KnowledgeWikiUploadDTO {

    @Nullable
    private String visibility;

    private String dir;

    private Boolean sensitive;

    private List<WikiInfo> wikiList;

    @Nullable
    @Size(max = 20, message = "cityList不能超过20个")
    private List<String> cityList;

    @Nullable
    @Size(max = 20, message = "tags不能超过20个")
    @StringListElementLength(maxLength = 32, message = "每个标签最多不能超过32个字符")
    private List<String> businessTags;

    @Nullable
    private List<Long> applicationList;

    @NotEmpty(message = "spaceCode不能为空")
    private String spaceCode;

    @Data
    public static class WikiInfo {

        @NotNull
        private String wikiUrl;

        @NotNull
        private Integer searchRangeCode;

        private String searchRangeDesc;
    }
}
