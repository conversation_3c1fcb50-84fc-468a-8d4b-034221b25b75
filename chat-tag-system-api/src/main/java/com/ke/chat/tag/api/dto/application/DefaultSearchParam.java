package com.ke.chat.tag.api.dto.application;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class DefaultSearchParam {

    private SearchSimilarity similarity;

    private SearchNum num;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class SearchSimilarity {

        private Float defaultValue;

        private Float max;

        private Float min;

    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class SearchNum {

        private Integer defaultValue;

        private Integer max;

        private Integer min;

    }
}
