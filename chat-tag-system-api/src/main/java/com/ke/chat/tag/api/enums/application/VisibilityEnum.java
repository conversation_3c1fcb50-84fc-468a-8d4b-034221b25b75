package com.ke.chat.tag.api.enums.application;

import lombok.Getter;

/**
 * <AUTHOR>
 */
@Getter
public enum VisibilityEnum {

    PUBLIC("public", "公开"),
    PRIVATE("private", "私有");

    private final String name;
    private final String desc;

    VisibilityEnum(String name, String desc) {
        this.name = name;
        this.desc = desc;
    }

    public static VisibilityEnum fromName(String name) {
        for (VisibilityEnum visibility : VisibilityEnum.values()) {
            if (visibility.name.equalsIgnoreCase(name)) {
                return visibility;
            }
        }
        throw new IllegalArgumentException("No enum constant with name " + name);
    }
}
