package com.ke.chat.tag.api.enums.application;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

@AllArgsConstructor
@Getter
@SuppressWarnings("all")
public enum NoKnowledgeStrategy {

    /**
     * AUTONOMOUS 自主回答
     * SEMI_AUTONOMOUS 半自主回答
     * RIGOROUS 严谨回答
     */
    AUTONOMOUS("autonomous"), SEMI_AUTONOMOUS("semi_autonomous"),RIGOROUS("rigorous");
    private final String name;

    private static Map<String,NoKnowledgeStrategy> map = new HashMap<String,NoKnowledgeStrategy>();

    static {
        for (NoKnowledgeStrategy strategy : NoKnowledgeStrategy.values()) {
            map.put(strategy.getName(), strategy);
        }
    }

    public static NoKnowledgeStrategy of(String strategy) {
        NoKnowledgeStrategy noKnowledgeStrategy = map.get(strategy);
        if (noKnowledgeStrategy == null) {
            throw new IllegalArgumentException("unknown strategy: " + strategy);
        }
        return noKnowledgeStrategy;
    }
}
