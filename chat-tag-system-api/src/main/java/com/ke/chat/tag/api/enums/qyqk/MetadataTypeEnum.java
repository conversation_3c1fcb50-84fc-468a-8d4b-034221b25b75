package com.ke.chat.tag.api.enums.qyqk;

import lombok.AllArgsConstructor;

@AllArgsConstructor
public enum MetadataTypeEnum {

    CHALLENGE_LIST("challenge_list","挑战列表"),
    CHALLENGE_DETAIL("challenge_detail", "挑战明细"),
    TASK_LIST("task_list", "任务列表"),
    TASK_DETAIL("task_detail", "任务明细"),
    CATEGORY_LIST("category_list", "类目列表"),
    ROLE_LIST("role_list", "角色列表"),
    CHALLENGE_RESULT("challenge_result","挑战结果"),
        ;

    public final String type;
    public final String desc;
}
