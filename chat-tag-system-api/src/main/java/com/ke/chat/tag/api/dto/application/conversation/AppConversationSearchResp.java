package com.ke.chat.tag.api.dto.application.conversation;

import com.ke.risk.safety.common.util.date.DateUtil;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.List;

import static com.ke.chat.tag.api.util.DateUtil.DATE_TIME_FORMAT;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class AppConversationSearchResp {

    /**
     * 主键id
     */
    private Long id;
    /**
     * 会话id
     */
    private String convId;

    /**
     * 会话展示内容（默认最后一次发起提问内容）
     */
    private String content;

    /**
     * 类型：今天、收藏、历史会话
     */
    private String type;

    /**
     * 类型描述
     */
    private String typeDesc;

    /**
     * 最后一次会话的时间
     */
    private String logtime;

    /**
     * 时间显示字符串
     */
    private String logtimeStr;

    /**
     * 请求用的modelName，现在切换model会新建会话，直接那会话记录里面的，如果切换模型不新建会话需要拿answer里面的
     */
    private List<ModelNameAndThread> modelNameAndThreads;

    /**
     * 取当前时间到最后一次该对话交互的时间差；
     * 一分钟以内展示“刚刚”；
     * 1分钟到1小时向下取整到分钟，展示xx分钟前；
     * 1小时~24小时向下取整到小时，展示xx小时前；
     * 超出24小时向下取整到天，展示x天前
     */
    public void initLogtimeStr() {

        if (logtime == null) {
            return;
        }
        long diff = Math.abs(DateUtil.getDateDiffByNow(logtime, DATE_TIME_FORMAT));
        if (diff < 60 * 1000) {
            logtimeStr = "刚刚";
        } else if (diff < 60 * 60 * 1000) {
            long m = diff / 60 / 1000;
            logtimeStr = m + "分钟前";
        } else if (diff < 24 * 60 * 60 * 1000) {
            long h = diff / 60 / 60 / 1000;
            logtimeStr = h + "小时前";
        } else {
            long d = diff / 24 / 60 / 60 / 1000;
            logtimeStr = d + "天前";
        }
    }

    @Getter
    @AllArgsConstructor
    public static class ModelNameAndThread {
        private String modelName;
        private String threadId;
    }
}
