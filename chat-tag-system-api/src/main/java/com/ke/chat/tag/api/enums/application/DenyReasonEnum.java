package com.ke.chat.tag.api.enums.application;

import com.google.common.collect.ImmutableList;
import lombok.Getter;

import java.util.List;

@Getter
public enum DenyReasonEnum {

    OTHER(1, "其他"),

    EXPECT_NOT_FOUND(2, "没有想要的结果"),

    WRONG_MESSAGE(3, "事实错误"),

    MAKE_NO_SENSE(4, "信口胡说"),

    NOT_COMPREHENSIVE(5, "不够全面");

    private Integer reasonCode;

    private String reasonName;

    DenyReasonEnum(Integer reasonCode, String reasonName) {
        this.reasonCode = reasonCode;
        this.reasonName = reasonName;
    }

    public static DenyReasonEnum getByReasonCode(Integer reasonCode) {
        for (DenyReasonEnum denyReasonEnum : DenyReasonEnum.values()) {
            if (denyReasonEnum.getReasonCode().equals(reasonCode)) {
                return denyReasonEnum;
            }
        }
        return null;
    }

    public static List<Integer> getAllDenyReasonCodes() {
        return ImmutableList.of(
                DenyReasonEnum.OTHER.getReasonCode(),
                DenyReasonEnum.EXPECT_NOT_FOUND.getReasonCode(),
                DenyReasonEnum.WRONG_MESSAGE.getReasonCode(),
                DenyReasonEnum.MAKE_NO_SENSE.getReasonCode(),
                DenyReasonEnum.NOT_COMPREHENSIVE.getReasonCode()
       );
    }



}
