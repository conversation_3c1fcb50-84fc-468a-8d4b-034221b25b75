package com.ke.chat.tag.api.enums.application;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum SseEventEnum {

    MESSAGE("message", "消息"),
    MESSAGE_REFRESH("message.refresh", "消息刷新"),
    CLOSE("close", "关闭"),
    ERROR("error", "异常"),
    CANCELLED("cancelled", "异常"),
    HEALTHY("healthy", "健康检查"),

    TOOL("toolCall", "工具调用");

    private String name;
    private String message;
}
