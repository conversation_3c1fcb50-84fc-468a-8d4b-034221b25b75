package com.ke.chat.tag.api.dto.task.image_score;

import com.ke.chat.tag.api.dto.task.TaskWithNo;
import lombok.Data;

import javax.annotation.Nullable;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * 图片打分任务，批量创建数据格式
 *
 * <AUTHOR>
 * @data 2023/4/24 11:40
 * @description
 */
@Data
public class ImageScoreTaskUploadDTO implements TaskWithNo {

    //任务编号为空时，由后端生成
    @Nullable
    private String taskNo;
    //原始实勘图片，可能为空
    @Nullable
    private String originImgUrl;
    @Size(min = 1, message = "至少包含一个图片标注问题")
    private List<ScoreTaskImage> imgList;

    @Data
    public static class ScoreTaskImage {

        //图片url，非加签，基础url
        //后端返回用户侧时，添加签名
        @NotEmpty(message = "图片url不能为空")
        private String imgUrl;
        //图片id，区分多个标注图片
        @NotEmpty(message = "图片id不能为空")
        private String imgId;

        @NotEmpty(message = "图片标题不能为空")
        private String title;

        //透传信息
        @Nullable
        private String extraInfo;

        //帮助算法侧透传
        @Nullable
        private String condTime;
        //帮助算法侧透传
        @Nullable
        private String pipeTime;
        //图片下，标注问题
        @Size(min = 1, message = "至少包含一个标注问题")
        private List<ScoreTaskQuestion> questionList;
    }

    @Data
    public static class ScoreTaskQuestion {

        //提问id
        @NotEmpty(message = "问题编号不能为空")
        private String questionId;
        //提问名称
        @NotEmpty(message = "问题名称不能为空")
        private String questionName;
        //问题选项
        @Size(min = 1, message = "至少包含一个选项")
        private List<ScoreTaskOption> optionList;
    }

    /**
     * 具体选项
     */
    @Data
    public static class ScoreTaskOption {

        // 选项id
        @NotEmpty(message = "选项id不能为空")
        private String optionId;
        //选项名称
        @NotEmpty(message = "选项名称不能为空")
        private String optionName;

    }
}
