package com.ke.chat.tag.api.util;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
public class ListUtil {

    public static <T> List<T> pageList(List<T> list, int pageSize, int pageNumber) {
        if (list == null || list.isEmpty()) {
            return Collections.emptyList(); // 返回一个空List表示空结果
        }

        if (pageSize <= 0) {
            throw new IllegalArgumentException("每页元素数量必须大于0");
        }

        int totalPages = (int) Math.ceil((double) list.size() / pageSize);

        if (pageNumber <= 0 || pageNumber > totalPages) {
            return Collections.emptyList(); // 返回一个空List表示该页没有数据
        }

        return list.stream()
            .skip((long) (pageNumber - 1) * pageSize)
            .limit(pageSize)
            .collect(Collectors.toList());
    }
}
