package com.ke.chat.tag.api.enums.exam;

import lombok.Getter;

@Getter
public enum AiExamGenerationStatus {

    QUEUED("queued", "排队中"),
    GENERATING("generating", "生成中"),
    SUCCESS("succeeded", "生成成功"),
    DELETED("deleted", "已删除"),

    FAILED("failed", "生成失败");

    private String type;
    private String desc;

    AiExamGenerationStatus(String type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public static boolean isSuccess(String status) {
        return AiExamGenerationStatus.SUCCESS.getType().equals(status);
    }

    public static boolean isFailed(String status) {
        return AiExamGenerationStatus.FAILED.getType().equals(status);
    }
}
