package com.ke.chat.tag.api.enums;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2023/7/7 10:25 上午
 **/
public enum KnowledgeSourceEnum {
    DOCUMENT("document"),
    QA("qa");

    public final String code;

    private static Map<String, KnowledgeSourceEnum> map = new HashMap<>();

    static {
        for (KnowledgeSourceEnum sourceEnum : KnowledgeSourceEnum.values()) {
            map.put(sourceEnum.code, sourceEnum);
        }
    }

    KnowledgeSourceEnum(String code) {
        this.code = code;
    }

    public static Optional<KnowledgeSourceEnum> getByCode(String code) {
        return Optional.ofNullable(map.get(code));
    }


}
