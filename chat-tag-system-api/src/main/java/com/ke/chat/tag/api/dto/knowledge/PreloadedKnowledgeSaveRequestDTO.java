package com.ke.chat.tag.api.dto.knowledge;

import lombok.Data;

import java.util.List;

/**
 * 保存预录入知识 DTO
 *
 * <AUTHOR>
 * @date 2024/11/13
 */
@Data
public class PreloadedKnowledgeSaveRequestDTO {
    /**
     * 问题数组
     */
    private List<String> questions;

    /**
     * 答案
     */
    private String answer;

    /**
     * 城市列表
     */
    private List<String> cityList;

    /**
     * 图片列表
     */
    private List<String> imageList;

    /**
     * 标签
     */
    private List<String> tagList;

    /**
     * 文件id
     */
    private String fileId;

    /**
     * 操作人ucid
     */
    private String operatorUcid;

    /**
     * 请求id，业务方传入幂等使用
     */
    private String requestId;

    /**
     * token的所属者
     */
    private String tokenUcid;
}
