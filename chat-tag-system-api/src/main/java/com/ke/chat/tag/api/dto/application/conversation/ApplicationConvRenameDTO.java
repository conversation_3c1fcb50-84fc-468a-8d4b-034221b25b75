package com.ke.chat.tag.api.dto.application.conversation;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ApplicationConvRenameDTO {

    @NotNull(message = "conversation must not be blank")
    private String convId;

    @NotNull(message = "applicationId must not be blank")
    private Long applicationId;

    @NotNull(message = "conversation name must not be blank")
    private String convName;

}
