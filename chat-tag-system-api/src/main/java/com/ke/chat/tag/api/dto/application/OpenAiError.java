package com.ke.chat.tag.api.dto.application;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@SuperBuilder(toBuilder = true)
public class OpenAiError {

    public OpenAiError(String type, String message) {
        this.message = message;
        this.type = type;
    }

    private String message;
    private String type;
    private String param;
    private String code;
}
