package com.ke.chat.tag.api.dto.application;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class OpenAIChatCompletionResponse {

    private List<Choice> choices;
    /**
     * 时间戳
     */
    private Integer created;
    /**
     * 唯一id
     */
    private String id;
    /**
     * 调用模型
     */
    private String model;

    /**
     * 调用接口
     */
    private String object;

    private Usage usage;

    private Object sensitives;

    @Data
    public static class Usage {

        private int completion_tokens;
        private int prompt_tokens;
        private int total_tokens;
    }

    /**
     * 错误
     */
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private OpenAiError error;

    @Data
    public static class Choice {

        /**
         * Every response will include a finish_reason. The possible values for finish_reason are:
         * <p>
         * stop: API returned complete message, or a message terminated by one of the stop sequences provided via the stop parameter length: Incomplete model output due to max_tokens parameter or token limit function_call: The model decided to call a function content_filter: Omitted content due to a
         * flag from our content filters null: API response still in progress or incomplete
         */
        private String finish_reason;
        private int index;
        private RequestMessage message;
    }
}
