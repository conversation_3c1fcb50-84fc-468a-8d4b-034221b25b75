package com.ke.chat.tag.api.enums;

public enum AssistantApplicationSaveModeEnum {
    SAVE_MEMORY_MODE("saveMode", "保存当次会话内容"),
    DISCARD_MEMORY_MODE("discardMode", "不保存当次会话内容");

    public final String type;
    public final String desc;

    AssistantApplicationSaveModeEnum(String type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public String getType() {
        return type;
    }

    public String getDesc() {
        return desc;
    }
}
