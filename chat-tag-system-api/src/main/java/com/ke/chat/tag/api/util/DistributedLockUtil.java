package com.ke.chat.tag.api.util;

import com.ke.boot.common.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;
import java.util.function.Supplier;

/**
 * 分布式锁工具类
 */
@Component
@Slf4j
public class DistributedLockUtil {

    @Autowired
    private RedissonClient redissonClient;

    // 默认锁等待时间（秒）
    @Value("${distributed.lock.wait-time:10}")
    private long defaultLockWaitTime;

    // 默认锁租约时间（秒）
    @Value("${distributed.lock.lease-time:30}")
    private long defaultLockLeaseTime;

    // 默认重试次数
    @Value("${distributed.lock.retry-times:3}")
    private int defaultRetryTimes;

    // 默认重试间隔（毫秒）
    @Value("${distributed.lock.retry-interval:500}")
    private long defaultRetryInterval;

    // 锁监控阈值（毫秒），超过此时间将记录告警日志
    @Value("${distributed.lock.monitor-threshold:5000}")
    private long lockMonitorThreshold;

    // 锁前缀，用于隔离不同应用
    private static final String LOCK_PREFIX = "distributed_lock:";

    // 业务异常的类名，用于识别哪些异常应该直接抛出
    private static final String BUSINESS_EXCEPTION_CLASS_NAME = "BusinessException";

    /**
     * 在锁保护下执行任务（使用默认配置）
     *
     * @param lockKey 锁的键
     * @param task    要执行的任务
     * @param <T>     返回值类型
     * @return 任务执行结果
     * @throws LockException 当锁操作失败时
     * @throws BusinessException 当任务执行过程中抛出业务异常时
     * @throws RuntimeException 当任务执行过程中发生其他运行时异常时
     */
    public <T> T executeWithLock(String lockKey, Supplier<T> task) throws LockException, BusinessException, RuntimeException {
        return executeWithLock(lockKey, task, defaultLockWaitTime, defaultLockLeaseTime, defaultRetryTimes);
    }

    /**
     * 在锁保护下执行任务（使用默认配置）
     *
     * @param lockKey 锁的键
     * @param task    要执行的任务
     * @throws LockException 当锁操作失败时
     * @throws BusinessException 当任务执行过程中抛出业务异常时
     * @throws RuntimeException 当任务执行过程中发生其他运行时异常时
     */
    public void executeWithLock(String lockKey, Runnable task) throws LockException, BusinessException, RuntimeException {
        executeWithLock(lockKey, () -> {
            task.run();
            return null;
        });
    }

    /**
     * 在锁保护下执行任务（自定义等待时间和租约时间）
     *
     * @param lockKey   锁的键
     * @param task      要执行的任务
     * @param waitTime  等待锁的最长时间（秒）
     * @param leaseTime 锁的租约时间（秒）
     * @param <T>       返回值类型
     * @return 任务执行结果
     * @throws LockException 当锁操作失败时
     * @throws BusinessException 当任务执行过程中抛出业务异常时
     * @throws RuntimeException 当任务执行过程中发生其他运行时异常时
     */
    public <T> T executeWithLock(String lockKey, Supplier<T> task, long waitTime, long leaseTime)
        throws LockException, BusinessException, RuntimeException {
        return executeWithLock(lockKey, task, waitTime, leaseTime, defaultRetryTimes);
    }

    /**
     * 在锁保护下执行任务（完全自定义参数）
     *
     * @param lockKey    锁的键
     * @param task       要执行的任务
     * @param waitTime   等待锁的最长时间（秒）
     * @param leaseTime  锁的租约时间（秒）
     * @param retryTimes 重试次数
     * @param <T>        返回值类型
     * @return 任务执行结果
     * @throws LockException 当锁操作失败时
     * @throws BusinessException 当任务执行过程中抛出业务异常时
     * @throws RuntimeException 当任务执行过程中发生其他运行时异常时
     */
    public <T> T executeWithLock(String lockKey, Supplier<T> task, long waitTime, long leaseTime, int retryTimes)
        throws LockException, BusinessException, RuntimeException {
        String fullLockKey = buildLockKey(lockKey);

        // 重试逻辑
        Exception lastException = null;
        for (int attempt = 0; attempt <= retryTimes; attempt++) {
            if (attempt > 0) {
                LOGGER.info("尝试获取锁重试 {}/{}: {}", attempt, retryTimes, fullLockKey);
                try {
                    Thread.sleep(defaultRetryInterval);
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                    throw new LockException("获取锁过程被中断: " + fullLockKey, ie);
                }
            }

            try {
                return doExecuteWithLock(fullLockKey, task, waitTime, leaseTime);
            } catch (LockTimeoutException e) {
                lastException = e;
                // 仅在锁获取超时时重试
                LOGGER.warn("获取锁超时，准备重试: {}", fullLockKey);
            } catch (BusinessException e) {
                // 业务异常直接向上抛出，不重试
                LOGGER.warn("捕获到业务异常，直接向上抛出: {}", e.getClass().getName());
                throw e;
            } catch (RuntimeException e) {
                // 检查是否为业务异常，如果是则直接抛出
                if (isBusinessException(e)) {
                    LOGGER.warn("捕获到业务异常，直接向上抛出: {}", e.getClass().getName());
                    throw e;
                }
                // 其他运行时异常包装后抛出，不重试
                throw new LockException("锁保护的任务执行失败", e);
            }
        }

        // 所有重试都失败
        LOGGER.error("经过 {} 次尝试后仍无法获取锁: {}", retryTimes, fullLockKey);
        throw new LockException("无法获取锁，请稍后重试", lastException);
    }

    /**
     * 判断异常是否为业务异常
     *
     * @param e 捕获的异常
     * @return 是否为业务异常
     */
    private boolean isBusinessException(Exception e) {
        // 检查异常类名是否匹配业务异常
        return e.getClass().getSimpleName().equals(BUSINESS_EXCEPTION_CLASS_NAME) ||
            // 检查异常是否由业务异常引起
            (e.getCause() != null && e.getCause().getClass().getSimpleName().equals(BUSINESS_EXCEPTION_CLASS_NAME));
    }

    /**
     * 实际执行加锁和任务的核心方法
     */
    private <T> T doExecuteWithLock(String fullLockKey, Supplier<T> task, long waitTime, long leaseTime) {
        RLock lock = redissonClient.getLock(fullLockKey);
        boolean lockAcquired = false;
        long startTime = System.currentTimeMillis();

        try {
            LOGGER.info("尝试获取锁: {}, 等待时间: {}秒, 租约时间: {}秒", fullLockKey, waitTime, leaseTime);
            lockAcquired = lock.tryLock(waitTime, leaseTime, TimeUnit.SECONDS);

            if (!lockAcquired) {
                LOGGER.warn("获取锁失败: {}", fullLockKey);
                throw new LockTimeoutException("无法获取锁: " + fullLockKey);
            }

            long acquireTime = System.currentTimeMillis() - startTime;
            if (acquireTime > lockMonitorThreshold) {
                LOGGER.warn("获取锁耗时过长: {}, 耗时: {}ms", fullLockKey, acquireTime);
            }

            LOGGER.info("成功获取锁: {}, 耗时: {}ms", fullLockKey, acquireTime);

            // 执行受保护的任务
            long taskStartTime = System.currentTimeMillis();
            T result = task.get();
            long taskExecutionTime = System.currentTimeMillis() - taskStartTime;

            // 监控任务执行时间，如果接近租约时间，记录警告
            if (taskExecutionTime > leaseTime * 1000 * 0.8) {
                LOGGER.warn("任务执行时间接近锁的租约时间: {}, 执行时间: {}ms, 租约时间: {}ms",
                    fullLockKey, taskExecutionTime, leaseTime * 1000);
            }

            LOGGER.info("锁保护的任务执行完成: {}, 任务耗时: {}ms", fullLockKey, taskExecutionTime);
            return result;

        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            LOGGER.error("获取锁过程被中断: {}", fullLockKey, e);
            throw new LockException("操作被中断，请稍后重试", e);
        } catch (LockTimeoutException e) {
            // 向上层传递锁超时异常，可能会进行重试
            throw e;
        } catch (BusinessException e) {
            // 业务异常直接向上抛出
            LOGGER.warn("业务逻辑抛出业务异常，将直接向上抛出: {}", fullLockKey);
            throw e;
        } catch (RuntimeException e) {
            // 检查是否为业务异常
            if (isBusinessException(e)) {
                LOGGER.warn("业务逻辑抛出业务异常，将直接向上抛出: {}", fullLockKey);
                throw e;  // 业务异常直接向上抛出，不包装
            }
            LOGGER.error("锁保护的任务执行异常: {}", fullLockKey, e);
            throw new LockException("锁保护的任务执行失败", e);
        } finally {
            // 安全释放锁
            try {
                if (lockAcquired && lock.isHeldByCurrentThread()) {
                    lock.unlock();
                    LOGGER.info("释放锁: {}, 总持有时间: {}ms", fullLockKey, (System.currentTimeMillis() - startTime));
                }
            } catch (Exception e) {
                LOGGER.error("释放锁时发生错误: {}", fullLockKey, e);
            }
        }
    }

    /**
     * 构建完整的锁键
     *
     * @param lockKey 业务锁键
     * @return 带前缀的完整锁键
     */
    private String buildLockKey(String lockKey) {
        // 确保锁键不为空
        if (lockKey == null || lockKey.trim().isEmpty()) {
            throw new IllegalArgumentException("锁键不能为空");
        }
        return LOCK_PREFIX + lockKey;
    }

    /**
     * 分布式锁自定义异常
     */
    public static class LockException extends RuntimeException {
        public LockException(String message) {
            super(message);
        }

        public LockException(String message, Throwable cause) {
            super(message, cause);
        }
    }

    /**
     * 锁获取超时异常，用于触发重试逻辑
     */
    private static class LockTimeoutException extends LockException {
        public LockTimeoutException(String message) {
            super(message);
        }
    }
}
