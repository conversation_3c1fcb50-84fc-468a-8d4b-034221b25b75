package com.ke.chat.tag.api.enums;

public enum AssistantApplicationEnvEnum {
    PROD(0, "prod"),
    TEST(1, "test");

    public final int code;
    public final String desc;

    AssistantApplicationEnvEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @SuppressWarnings("all")
    public static AssistantApplicationEnvEnum of(String desc) {
        for (AssistantApplicationEnvEnum value : AssistantApplicationEnvEnum.values()) {
            if (value.desc.equals(desc)) {
                return value;
            }
        }
        throw new IllegalArgumentException("unknown desc: " + desc);
    }
}
