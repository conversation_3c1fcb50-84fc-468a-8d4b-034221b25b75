package com.ke.chat.tag.api.enums.knowledge;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/11/21
 */
public enum PreloadedKnowledgeStatusEnum {
    NOT_APPLIED(0, "未应用"),
    DELETED(-1, "已删除"),
    APPLIED(1, "已应用");


    public final Integer code;
    public final String desc;

    private static Map<Integer, PreloadedKnowledgeStatusEnum> map = new HashMap<>();


    PreloadedKnowledgeStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }


    static {
        for (PreloadedKnowledgeStatusEnum statusEnum : PreloadedKnowledgeStatusEnum.values()) {
            map.put(statusEnum.code, statusEnum);
        }
    }

    public static PreloadedKnowledgeStatusEnum getByCode(Integer code) {
        return map.get(code);
    }
}

