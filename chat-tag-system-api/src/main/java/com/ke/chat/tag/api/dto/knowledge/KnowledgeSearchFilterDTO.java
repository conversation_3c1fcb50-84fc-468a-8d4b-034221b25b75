package com.ke.chat.tag.api.dto.knowledge;

import com.ke.chat.tag.api.dto.application.ApplicationValidationReqDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class KnowledgeSearchFilterDTO {

    private List<String> fileTypeList;
    private List<String> businessTypeList;
    private List<ApplicationValidationReqDTO> applicationList;
    private List<FileInfoDTO> fileList;

}
