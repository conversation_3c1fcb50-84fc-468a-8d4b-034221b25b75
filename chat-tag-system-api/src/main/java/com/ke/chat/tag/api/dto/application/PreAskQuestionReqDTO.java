package com.ke.chat.tag.api.dto.application;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class PreAskQuestionReqDTO {

    @NotNull
    private Long applicationId;

    private String convId;

    private String questionId;

    @NotNull
    private String content;

    @NotNull
    private String messageType;

    private String model;
}
