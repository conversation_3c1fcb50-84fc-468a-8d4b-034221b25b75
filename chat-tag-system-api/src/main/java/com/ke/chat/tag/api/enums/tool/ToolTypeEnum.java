package com.ke.chat.tag.api.enums.tool;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum ToolTypeEnum {

    HOUSING("经纪", "https://file.ljcdn.com/cv-aigc/belle/3fd7ecb47509466c8948325cbbb2af11/%E5%AE%B9%E5%99%A8-5.png?ak=Q265N5ELG32TT7UWO8YJ&exp=259200&ts=1711963013&sign=fb217bcd8455aaf877617815151190aa"
        , "https://file.ljcdn.com/cv-aigc/belle/34fe84b766554af380af6c4dc6910138/%E5%AE%B9%E5%99%A8-6.png?ak=Q265N5ELG32TT7UWO8YJ&exp=259200&ts=1711963425&sign=7b4ce7c9b81cada42812bce3a0b2f9bf"),
    DECORATION("整装", "https://file.ljcdn.com/cv-aigc/belle/ca14f27e0b374806a048d5819c20fd18/%E5%AE%B9%E5%99%A8-2.png?ak=Q265N5ELG32TT7UWO8YJ&exp=259200&ts=1711963442&sign=899cde5bc04c1a75726c8053057ad706"
        , "https://file.ljcdn.com/cv-aigc/belle/ddd267d434004fe3a64c00c87468d562/%E5%AE%B9%E5%99%A8-7.png?ak=Q265N5ELG32TT7UWO8YJ&exp=259200&ts=1711963465&sign=b18b9dc83fbe0b1d5779a6f1df823c38"),
    DWELL("惠居", "https://file.ljcdn.com/cv-aigc/belle/d3af143fffde4ca8ad5851b754b252f3/%E5%AE%B9%E5%99%A8-3.png?ak=Q265N5ELG32TT7UWO8YJ&exp=259200&ts=1711963486&sign=0d72611d8380a4eff9dba2f061c96653"
        , "https://file.ljcdn.com/cv-aigc/belle/56ae4990d51e40cf954f587836004f04/%E5%AE%B9%E5%99%A8-8.png?ak=Q265N5ELG32TT7UWO8YJ&exp=259200&ts=1711963506&sign=f94b13ec44b74f926222ac7764cf20a1"),
    OTHER("通用", "https://file.ljcdn.com/cv-aigc/belle/1df826e2a2924c8ba12642f245624a8b/%E5%AE%B9%E5%99%A8.png?ak=Q265N5ELG32TT7UWO8YJ&exp=259200&ts=1711963526&sign=be35f62ee6a91acc174b2163de6a575d"
        , "https://file.ljcdn.com/cv-aigc/belle/9ea24b55a2ab4484a95620b7d3fc7a61/%E5%AE%B9%E5%99%A8-4.png?ak=Q265N5ELG32TT7UWO8YJ&exp=259200&ts=1711963545&sign=9f8280903feb8910e2ec3e63f3b00983");

    private String type;
    private String icon;
    private String hoverIcon;
}
