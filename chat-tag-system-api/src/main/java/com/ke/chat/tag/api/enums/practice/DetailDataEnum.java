package com.ke.chat.tag.api.enums.practice;

import com.ke.chat.tag.api.dto.practice.EvaluationManagerDTO;
import com.ke.chat.tag.api.dto.practice.FeedbackManagerDTO;
import com.ke.chat.tag.api.dto.practice.PromptManagerDTO;
import com.ke.chat.tag.api.dto.practice.ResistanceProblemDTO;
import com.ke.chat.tag.api.dto.practice.ScoreManagerDTO;
import com.ke.chat.tag.api.dto.practice.ScriptDescriptionDTO;
import com.ke.chat.tag.api.dto.practice.TtsAudioManagerDTO;
import com.ke.chat.tag.api.dto.practice.challenge.ChallengeDetailCheck;
import lombok.Getter;

@Getter
public enum DetailDataEnum {

    TTS_AUDIO_MANAGER_DATA("ttsAudioManager", TtsAudioManagerDTO.class),
    SCRIPT_DESCRIPTION_DATA("scriptDescription", ScriptDescriptionDTO.class),
    PROMPT_MANAGER("promptManager", PromptManagerDTO.class),
    SCORE_MANAGER("scoreManager", ScoreManagerDTO.class),
    EVALUATION_MANAGER("evaluationManager", EvaluationManagerDTO.class),
    RESISTANCE_PROBLEM("resistanceProblem", ResistanceProblemDTO.class),
    FEEDBACK_MANAGER("feedbackManager", FeedbackManagerDTO.class),
    ;


    private String property;

    private Class<? extends ChallengeDetailCheck> dtoClass;

    DetailDataEnum(String property, Class<? extends ChallengeDetailCheck> dtoClass) {
        this.property = property;
        this.dtoClass = dtoClass;
    }

    public static DetailDataEnum getDateEnum(String property) {
        for (DetailDataEnum value : DetailDataEnum.values()) {
            if (value.getProperty().equals(property)) {
                return value;
            }
        }
        return null;
    }
}
