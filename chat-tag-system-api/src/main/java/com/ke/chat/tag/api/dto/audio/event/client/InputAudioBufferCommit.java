package com.ke.chat.tag.api.dto.audio.event.client;

import com.ke.chat.tag.api.dto.audio.event.BaseEvent;
import lombok.Data;
import lombok.Getter;

import static com.ke.chat.tag.api.enums.audio.EventEnum.INPUT_AUDIO_BUFFER_COMMIT;

/**
 * 写入语音结束时，告诉服务端可以提交模型处理了,服务端将响应 input_audio_buffer.committed
 */
@Data
public class InputAudioBufferCommit extends BaseEvent {

    public final String type = INPUT_AUDIO_BUFFER_COMMIT.type;
}
