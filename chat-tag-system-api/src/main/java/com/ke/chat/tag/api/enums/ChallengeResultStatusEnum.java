package com.ke.chat.tag.api.enums;

import lombok.Getter;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: yumiao05
 * @Date: 2025/04/18/15:49
 * @Description:
 */
@Getter
public enum ChallengeResultStatusEnum {

    PASS("已通过", "pass"),

    UN_PASS("未通过","unPass");

    private final String status;
    private final String code;

    ChallengeResultStatusEnum(String status, String code) {
        this.status = status;
        this.code = code;
    }
}
