package com.ke.chat.tag.api.enums.practice;

import lombok.Getter;

@Getter
public enum ChallengeStageEnum {


    /**
     * 初始挑战设置阶段
     */
    INITIAL_SETUP(1,"initialSetup","初始挑战设置"),

    /**
     * 挑战过程控制阶段
     */
    PROCESS_CONTROL(2,"processControl","挑战过程控制"),

    /**
     * 挑战结果评价阶段
     */
    RESULT_EVALUATION(3,"resultEvaluation","挑战结果评价");


    /**
     * 代表阶段的数字
     */
    private Integer stageCode;
    /**
     * 挑战配置阶段类型
     */
    private String stageType;
    /**
     * 挑战配置阶段名称
     */
    private String stageName;

    ChallengeStageEnum(Integer stageCode, String stageType, String stageName) {
        this.stageCode = stageCode;
        this.stageType = stageType;
        this.stageName = stageName;
    }
}
