package com.ke.chat.tag.api.dto.application;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class RoleApplicationReqCreateDTO extends BaseApplicationReqCreateDTO {

    /**
     * 应用id
     */
    private Long applicationId;

    private String roleInstruction;

    private Boolean exampleQuestionsEnabled;

    private List<String> exampleQuestions;
}
