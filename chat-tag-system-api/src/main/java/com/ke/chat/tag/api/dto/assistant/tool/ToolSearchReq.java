package com.ke.chat.tag.api.dto.assistant.tool;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ToolSearchReq {

    private String searchKeyword; // 搜索关键词。支持模糊搜索工具集、工具、工具描述

    private String searchScope; // 搜索范围。默认为工具集、工具、工具描述；可按"toolCollectName,toolName,toolDesc"进行筛选

    private Boolean isFuzzyMatch; // 是否模糊匹配

    private String toolCollectName;

    private String category;

    private Boolean personal;

    private Integer currentLogin; //查询个人创建

    private Boolean favoritesOnly; // 仅显示收藏

    @NotNull
    private Integer pageNum;

    @NotNull
    private Integer pageSize;

    private Integer sortType; //排序类型 1-最新 2-最热

    private Integer toolType; //1:私域可见  2:公域可见

    private String ucid; //创建人ucid

    /**
     * 空间编码
     */
    @NotEmpty(message = "spaceCode不能为空")
    private String spaceCode;
}
