package com.ke.chat.tag.api.dto.application;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ApplicationStatisticsBaseReqDTO {
    @NotNull
    private Long applicationId;
    @NotNull
    private String startTime;
    @NotNull
    private String endTime;
    private Integer code;
}
