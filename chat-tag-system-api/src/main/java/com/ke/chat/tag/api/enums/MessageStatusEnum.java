package com.ke.chat.tag.api.enums;

import lombok.Getter;

import java.util.Arrays;

@Getter
public enum MessageStatusEnum {

    COMPLETED(0, "completed", "成功"),
    FAILED(1, "failed", "失败"),
    NO_REPLY(2, "no_reply", "未回复"),
    CUT(3, "cut", "截断"),
    CANCEL(4, "cancelled", "取消"),
    SENSITIVE(5, "sensitive", "包含敏感内容"),
    RESTRICTED(6, "restricted", "限流"),
    TIMEOUT(7, "timeout", "超时");

    public final int code;
    public final String type;
    public final String message;

    MessageStatusEnum(int code, String type, String message) {
        this.code = code;
        this.type = type;
        this.message = message;
    }

    public static String getTypeByCode(int code) {
        return Arrays.stream(MessageStatusEnum.values())
            .filter(i -> i.getCode() == code).findFirst()
            .map(MessageStatusEnum::getType)
            .orElse(COMPLETED.getType());
    }
}
