package com.ke.chat.tag.api.dto.application;

import com.ke.chat.tag.api.response.BasePageRequest;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.annotation.Nullable;
import javax.validation.constraints.NotEmpty;


/**
 * 应用信息查询实体
 *
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ApplicationInfosReqDTO extends BasePageRequest {

    /**
     * 排序规则
     */
    @Nullable
    private String order;

    /**
     * 检索内容
     * 支持模糊匹配
     */
    @Nullable
    private String criteria;

    @Nullable
    private Integer applicationId;

    @Nullable
    private Integer isRelateFile;

    @Nullable
    private Integer hasQA;

    @Nullable
    private String fileName;

    @NotEmpty(message = "spaceCode不能为空")
    private String spaceCode;
}
