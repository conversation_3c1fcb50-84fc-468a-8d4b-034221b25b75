package com.ke.chat.tag.api.bean;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@Data
@AllArgsConstructor
@NoArgsConstructor
@SuperBuilder(toBuilder = true)
public class OpenAiFile {

    private String id;

    private Long bytes;

    @JsonProperty("created_at")
    private Long createdAt;

    @JsonProperty("file_name")
    private String filename;

    private String object;

    private String purpose;

    @Deprecated
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String status;

    @Deprecated
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonProperty("status_details")
    private String statusDetails;

    private String url;
}
