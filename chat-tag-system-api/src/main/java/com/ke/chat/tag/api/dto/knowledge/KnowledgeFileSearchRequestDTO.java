package com.ke.chat.tag.api.dto.knowledge;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.lang.Nullable;

import javax.validation.constraints.NotEmpty;

/**
 * <AUTHOR>
 * @date 2023/7/12 5:12 下午
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class KnowledgeFileSearchRequestDTO {

    @Nullable
    private int pageNo;
    @Nullable
    private int pageSize;
    private String queryStr;
    @Nullable
    private String groupType = "";

    @NotEmpty(message = "spaceCode不能为空")
    private String spaceCode;

}
