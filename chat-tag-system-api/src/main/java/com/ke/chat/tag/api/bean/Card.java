package com.ke.chat.tag.api.bean;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 工具卡片，bella层定义的类，用于工具前端内容展示
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
public class Card {

    private int cardId;

    /**
     * 工具输出内容
     */
    private String data;

    /**
     * 卡片模板
     */
    private String template;

    /**
     * 卡片跳转链接
     */
    private String cardUrl;


    @JsonInclude(JsonInclude.Include.NON_EMPTY) // 只有当OperationNext非空且其属性非空时才序列化
    private OperationNext operation;

    @Data
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class OperationNext {
        /**
         * 输入
         */
        private String type;

        /**
         * 输出信息
         */
        public String methods;
    }

    }
