package com.ke.chat.tag.api.dto.application;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * @Author: gaopan034
 * @Date: 2024/11/4
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@Accessors(chain = true)
public class AppRecommendQuestionDTO {
    private Long applicationId;

    private String name;

    private String exampleQuestion;
}
