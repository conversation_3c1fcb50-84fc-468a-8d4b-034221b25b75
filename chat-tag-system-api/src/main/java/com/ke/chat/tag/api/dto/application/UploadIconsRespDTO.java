package com.ke.chat.tag.api.dto.application;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import lombok.experimental.SuperBuilder;

import java.util.List;


/**
 * <AUTHOR>
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
@SuperBuilder(toBuilder = true)
public class UploadIconsRespDTO {

    /**
     * 是否全部成功
     */
    private boolean allSuccess;

    /**
     * 当且仅当allSuccess为false时生效
     */
    private String errMsg;

    List<UploadIconsRespEntity> icons;

    @Data
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class UploadIconsRespEntity {

        /**
         * icon的url
         */
        private String icon;

        /**
         * icon的文件名
         */
        private String name;

        /**
         * icon状态 2: 使用中;1: 未使用;0: 失效
         */
        private Byte status;

        /**
         * 此icon是否上传成功
         */
        private boolean success;
    }

}
