package com.ke.chat.tag.api.enums;

import com.ke.chat.tag.api.exception.openapi.InvalidRequestError;
import com.ke.chat.tag.api.util.JsonUtil;
import lombok.Getter;

import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Getter
public enum OpenAPIFilePurposeEnum {

    fine_tune("fine-tune"),
    batch("batch"),
    fine_tune_results("fine-tune-results"),
    assistants("assistants"),
    ;

    public final String purpose;

    OpenAPIFilePurposeEnum(String purpose) {
        this.purpose = purpose;
    }

    public static OpenAPIFilePurposeEnum validate(String purpose) {
        List<OpenAPIFilePurposeEnum> list = Stream.of(OpenAPIFilePurposeEnum.values())
            .filter(openAPIFilePurposeEnum -> openAPIFilePurposeEnum.purpose.equals(purpose)).collect(Collectors.toList());
        if (list.isEmpty()) {
            throw new InvalidRequestError(
                String.format("\"%s\" is not one of %s - \"purpose\"", purpose, JsonUtil.parseBean2Str(Stream.of(OpenAPIFilePurposeEnum.values())
                    .map(OpenAPIFilePurposeEnum::getPurpose).collect(Collectors.toList()))));
        }
        return list.get(0);
    }

    public static OpenAPIFilePurposeEnum detectPurposeByFileId(String fileId) {
        if (fileId.startsWith("ft-")) {
            return OpenAPIFilePurposeEnum.fine_tune;
        } else if (fileId.startsWith("ft-result-")) {
            return OpenAPIFilePurposeEnum.fine_tune_results;
        } else if (fileId.startsWith("batch-")) {
            return OpenAPIFilePurposeEnum.batch;
        } else {
            return OpenAPIFilePurposeEnum.assistants;
        }
    }

}
