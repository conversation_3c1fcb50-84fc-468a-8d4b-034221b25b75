package com.ke.chat.tag.api.enums;

public enum EvaluationTypeEnum {
    //召回评测
    //可用性评测
    //全流程评测
    RECALL_EVALUATION("召回评测", "<View>\\n    <Style>\\n    </Style>\\n    <!-- 元信息 -->\\n    <!-- 不支持从List中获取， 下侧有任意name不为空的tag就会显示不出来 -->\\n    <View name=\\\"meta_data\\\">\\n        <Header value=\\\"Query\\\"/>\\n        <Text name=\\\"query\\\" value=\\\"$meta_data.recall_ability.recall.query\\\"></Text>\\n        <Header value=\\\"期待的召回内容\\\"/>\\n        <Text name=\\\"expected_recall\\\" value=\\\"$meta_data.recall_ability.recall.expected_content\\\"></Text>\\n        <Header value=\\\"实际的召回内容\\\"/>\\n        <Text name=\\\"recall\\\" value=\\\"$meta_data.recall_ability.recall.content\\\"></Text>\\n    </View>\\n    <View name=\\\"rating\\\">\\n        <Header value=\\\"请根据Query及期待召回内容，判断实际召回内容效果：\\\"/>\\n        <Header value=\\\"相关性\\\" size=\\\"3\\\"/>\\n        <Choices name=\\\"relation_score\\\" showInline=\\\"true\\\" toName=\\\"recall\\\" choice=\\\"single\\\"\\n                 required=\\\"true\\\">\\n            <Choice value=\\\"2分(完全相关)\\\"/>\\n            <Choice value=\\\"1分(大部分相关)\\\"/>\\n            <Choice value=\\\"0分(不相关)\\\"/>\\n        </Choices>\\n        <Header value=\\\"完整性\\\" size=\\\"3\\\"/>\\n        <Choices name=\\\"integrity_score\\\" showInline=\\\"true\\\" toName=\\\"recall\\\" choice=\\\"single\\\"\\n                 required=\\\"true\\\">\\n            <Choice value=\\\"2分(不多不少)\\\"/>\\n            <Choice value=\\\"1分(部分丢失/冗余)\\\"/>\\n            <Choice value=\\\"0分(大都丢失/冗余)\\\"/>\\n        </Choices>\\n    </View>\\n</View>"),
    USABILITY_EVALUATION("可用性评测", "<View>\\n    <Style>\\n    </Style>\\n    <!-- 元信息 -->\\n    <!-- 不支持从List中获取， 下侧有任意name不为空的tag就会显示不出来 -->\\n    <View name=\\\"meta_data\\\">\\n        <Header value=\\\"用户输入\\\"/>\\n        <Text name=\\\"input\\\" value=\\\"$meta_data.application_ability.application.input\\\"></Text>\\n        <Header value=\\\"期待输出\\\"/>\\n        <Text name=\\\"standard_output\\\" value=\\\"$meta_data.application_ability.application.standard_output\\\"></Text>\\n        <Header value=\\\"实际输出\\\"/>\\n        <Text name=\\\"actual_out_put\\\" value=\\\"$meta_data.application_ability.application.actual_output\\\"></Text>\\n    </View>\\n    <View name=\\\"rating\\\">\\n        <Header value=\\\"根据用户输入和期待输出，判断实际输出是否存在问题，及具体的问题是什么\\\" size=\\\"6\\\"/>\\n        <Choices name=\\\"wrong_reason\\\" toName=\\\"actual_out_put\\\" choice=\\\"single\\\"\\n                 required=\\\"true\\\">\\n            <Choice value=\\\"无结果\\\"/>\\n            <Choice value=\\\"有结果-结果可用\\\"/>\\n            <Choice value=\\\"有结果-不可用-文不对题\\\"/>\\n            <Choice value=\\\"有结果-不可用-自由发挥\\\"/>\\n          <Choice value=\\\"有结果-不可用-信息丢失\\\"/>\\n          <Choice value=\\\"有结果-不可用-信息冗余\\\"/>\\n          <Choice value=\\\"有结果-不可用-反问出错\\\"/>\\n            <Choice value=\\\"有结果-不可用-其他问题\\\"/>\\n        </Choices>\\n    </View>\\n</View>"),
    FULL_PROCESS_EVALUATION("全流程评测", "");
    public final String type;
    public final String labelConfig;

    EvaluationTypeEnum(String type, String labelConfig) {
        this.type = type;
        this.labelConfig = labelConfig;
    }

    public String getType() {
        return type;
    }

    public String getLabelConfig() {
        return labelConfig;
    }
}
