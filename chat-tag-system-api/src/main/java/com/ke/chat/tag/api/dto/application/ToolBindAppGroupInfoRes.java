package com.ke.chat.tag.api.dto.application;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * function:
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ToolBindAppGroupInfoRes {

    private List<ToolBindAppGroupInfoRes.AppInfo> appInfos;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class AppInfo {

        private Long outAppId;

        private String appName;

        private List<GroupInfo> groupInfos;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class GroupInfo{

        private String groupName;

        private String groupDepiction;

    }
}
