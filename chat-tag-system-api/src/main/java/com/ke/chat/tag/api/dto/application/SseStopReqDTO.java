package com.ke.chat.tag.api.dto.application;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.annotation.Nullable;
import javax.validation.constraints.NotEmpty;

/**
 * <AUTHOR>
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
public class SseStopReqDTO {

    @NotEmpty(message = "answerId must not be blank")
    private String answerId;

    @Nullable
    private Integer index;

    @Nullable
    private String convId;
}
