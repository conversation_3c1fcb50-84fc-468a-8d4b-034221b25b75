package com.ke.chat.tag.api.dto.application.conversation;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.annotation.Nullable;
import javax.validation.constraints.NotNull;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ApplicationConvReqDTO {

    @NotNull(message = "conversation must not be blank")
    private String convId;

    @NotNull(message = "applicationId must not be blank")
    private Long applicationId;

    @Nullable
    private Integer pageSize;

    @Nullable
    private Integer pageNum;
}
