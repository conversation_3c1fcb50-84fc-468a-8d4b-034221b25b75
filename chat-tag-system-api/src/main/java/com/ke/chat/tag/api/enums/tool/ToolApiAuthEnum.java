package com.ke.chat.tag.api.enums.tool;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;

/**
 * 验签类型：0-无验签 ；1-API Key；2-Base；3-Bearer
 */
@Getter
@AllArgsConstructor
public enum ToolApiAuthEnum {

    NONE(0, "无验签", "", "", ""),
    API_KEY(1, "API Key", "", "key", "ak"),
    BASE(2, "Base", "Basic ", "key", "ak"),
    BEARER(3, "Bearer", "Bearer ", "key", "ak"),
    IAM(4, "iam", "", "ak", "sk");

    private Integer code;
    private String type;
    private String prefix;
    private String keyName;
    private String valueName;

    public static String getKeyNameByType(int code) {
        return Arrays.stream(ToolApiAuthEnum.values()).filter(i -> i.getCode().equals(code))
            .findFirst().map(ToolApiAuthEnum::getKeyName).orElse(StringUtils.EMPTY);
    }

    public static String getValueNameByType(int code) {
        return Arrays.stream(ToolApiAuthEnum.values()).filter(i -> i.getCode().equals(code))
            .findFirst().map(ToolApiAuthEnum::getValueName).orElse(StringUtils.EMPTY);
    }
}
