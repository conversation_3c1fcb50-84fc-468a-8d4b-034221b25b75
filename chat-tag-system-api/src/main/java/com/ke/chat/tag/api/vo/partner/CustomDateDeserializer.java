package com.ke.chat.tag.api.vo.partner;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.ke.boot.common.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.Date;

/**
 * CustomDateDeserializer
 *
 * <AUTHOR>
 * @date 2025/3/5
 */
@Slf4j
public class CustomDateDeserializer extends JsonDeserializer<Date> {
    private static final DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd")
        .withZone(ZoneId.of("UTC"));

    @Override
    public Date deserialize(JsonParser p, DeserializationContext ctxt) throws IOException {
        String dateStr = p.getText().trim();
        try {
            LocalDate localDate = LocalDate.parse(dateStr, formatter);
            return Date.from(localDate.atStartOfDay(ZoneId.of("Asia/Shanghai")).toInstant());
        } catch (DateTimeParseException e) {
            LOGGER.warn("日期格式错误 '{}', 请使用格式：yyyy-MM-dd", dateStr, e);
            throw new BusinessException("400", "日期格式错误，请使用正确的格式yyyy-MM-dd");
        }
    }
}
