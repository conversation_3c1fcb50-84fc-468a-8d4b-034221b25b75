package com.ke.chat.tag.api.dto.application;

import com.ke.chat.tag.api.response.BasePageResponse;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ApplicationChatRecordResponseDTO extends BasePageResponse implements Serializable {
    List<ApplicationChatRecordDTO> qaList;

    public ApplicationChatRecordResponseDTO(Integer pageNum, Integer pageSize, Integer total) {
        super(pageSize, pageNum, total);
    }

}
