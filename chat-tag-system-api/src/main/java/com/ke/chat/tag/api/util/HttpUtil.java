package com.ke.chat.tag.api.util;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.URLUtil;
import okhttp3.Call;
import okhttp3.Callback;
import okhttp3.FormBody;
import okhttp3.MediaType;
import okhttp3.MultipartBody;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;
import okhttp3.ResponseBody;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.util.UriComponentsBuilder;

import javax.net.ssl.HostnameVerifier;
import javax.net.ssl.SSLContext;
import javax.net.ssl.SSLSession;
import javax.net.ssl.SSLSocketFactory;
import javax.net.ssl.TrustManager;
import javax.net.ssl.X509TrustManager;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.security.SecureRandom;
import java.security.cert.X509Certificate;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 *
 */
@SuppressWarnings({"BooleanMethodIsAlwaysInverted", "unused"})
public class HttpUtil {

    private static Logger logger = LoggerFactory.getLogger(HttpUtil.class);

    private Request.Builder builder;
    private OkHttpClient.Builder clientBuilder;

    public interface IRequest {
        void build(Request.Builder builder);
    }

    public interface IClient {
        void build(OkHttpClient.Builder builder);
    }

    public static HttpUtil create() {
        return new HttpUtil();
    }

    private HttpUtil() {
        builder = new Request.Builder();
        // 支持https请求，绕过验证
        X509TrustManager manager = getX509TrustManager();
        clientBuilder = new OkHttpClient.Builder()
            .connectTimeout(180, TimeUnit.SECONDS)
            .writeTimeout(180, TimeUnit.SECONDS)
            .readTimeout(180, TimeUnit.SECONDS)
            .pingInterval(180, TimeUnit.SECONDS)
            .sslSocketFactory(getSocketFactory(manager), manager)
            .hostnameVerifier(getHostnameVerifier());
    }

    private boolean isOK(String url) {
        return true;
    }

    public HttpUtil initRequest(IRequest request) {
        request.build(builder);
        return this;
    }

    public HttpUtil initClient(IClient client) {
        client.build(clientBuilder);
        return this;
    }

    /**
     * 忽略https请求时的ssl证书校验
     *
     * @return
     */
    public static SSLSocketFactory getSocketFactory(TrustManager manager) {
        SSLSocketFactory socketFactory = null;
        try {
            SSLContext sslContext = SSLContext.getInstance("SSL");
            sslContext.init(null, new TrustManager[]{manager}, new SecureRandom());
            socketFactory = sslContext.getSocketFactory();
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("getSocketFactory()", e);
        }
        return socketFactory;
    }

    /**
     * 忽略https请求时的ssl证书校验
     *
     * @return
     */
    public static X509TrustManager getX509TrustManager() {
        return new X509TrustManager() {
            @Override
            public void checkClientTrusted(X509Certificate[] chain, String authType) {

            }

            @Override
            public void checkServerTrusted(X509Certificate[] chain, String authType) {

            }

            @Override
            public X509Certificate[] getAcceptedIssuers() {
                return new X509Certificate[0];
            }
        };
    }

    /**
     * 忽略https请求时的ssl证书校验
     *
     * @return
     */
    public static HostnameVerifier getHostnameVerifier() {
        return new HostnameVerifier() {
            @Override
            public boolean verify(String s, SSLSession sslSession) {
                return true;
            }
        };
    }

    /**
     * 把 url 访问标示为从app里面发出去的访问
     *
     * @param url 原始url
     * @return 标记后的url
     */
    private String mark(String url) {
        return url;
    }

    /**
     * 把 post 方式访问接口的数据，追加一些信息
     */
    private <T> Map<String, T> mark(Map<String, T> data) {
        return data;
    }

    /**
     * 向srcUrl里面追加新的参数
     *
     * @param url    源url
     * @param params 参数
     * @return 新的url
     */
    private <T> String addUrlParam(String url, Map<String, T> params) {
        try {
            MultiValueMap<String, String> ps = new LinkedMultiValueMap<String, String>();
            for (String key : params.keySet()) {
                ps.add(URLUtil.encodeQuery(key), URLUtil.encodeQuery(params.get(key).toString()));
            }
            return UriComponentsBuilder.fromUriString(url).queryParams(ps).build().toString();
        } catch (Exception e) {
            logger.error("HttpUtil addUrlParam exception", e);
            return url;
        }
    }

    public String doGet(String url) {
        logger.info("doGet() url:{}", url);
        return execute(builder
            .url(mark(url))
            .build()
        );
    }

    public <T> String doGet(String url, Map<String, T> data) {
        logger.info("doGet() url:{}, data:{}", url, JsonUtil.parseBean2Str(data));
        return execute(builder
            .url(mark(addUrlParam(url, data)))
            .build()
        );
    }

    public <T> String doPost(String url, Map<String, T> data) {
        logger.info("doPost() url:{}, data:{}", url, JsonUtil.parseBean2Str(data));
        return execute(builder
            .url(mark(url))
            .post(getFormBody(mark(data)))
            .build()
        );
    }

    public String doPost(String url, String body) {
        logger.info("doPost() url:{}, body:{}", url, body);
        return execute(builder
            .url(mark(url))
            .post(RequestBody.create(MediaType.parse("application/json; charset=UTF-8"), body))
            .build()
        );
    }

    public <T> String doPut(String url, Map<String, T> data) {
        logger.info("doPut() url:{}, data:{}", url, JsonUtil.parseBean2Str(data));
        return execute(builder
            .url(mark(url))
            .put(getFormBody(mark(data)))
            .build()
        );
    }

    public <T> String doDelete(String url) {
        logger.info("doDelete() url:{}", url);
        return execute(builder
            .url(mark(url))
            .delete()
            .build()
        );
    }

    public <T> String doDelete(String url, String bodyStr) {
        logger.info("doDelete() url:{}, body:{}", url, bodyStr);
        RequestBody body = RequestBody.create(MediaType.parse("application/json; charset=utf-8"), bodyStr);  // 传递空JSON对象作为例子
        return execute(builder
            .url(mark(url))
            .delete(body)
            .build()
        );
    }

    /**
     * @param url      url
     * @param name     file param key
     * @param filepath file path to upload
     * @param data     other param key-value
     */
    public <T> String doUpload(String url, String name, String filepath, Map<String, T> data) {

        return doUpload(
            url,
            name,
            FileUtil.getName(filepath),
            data,
            RequestBody.create(
                MediaType.parse("application/octet-stream"),
                new File(filepath)
            )
        );
    }

    public <T> String doUpload(String url, String name, String filename, byte[] content, Map<String, T> data) {

        if (content == null || content.length == 0) {
            return null;
        }

        return doUpload(
            url,
            name,
            filename,
            data,
            RequestBody.create(
                MediaType.parse("application/octet-stream"),
                content
            )
        );
    }

    private <T> String doUpload(String url, String name, String filename, Map<String, T> data, RequestBody fileBody) {

        logger.info("doUpload() url:{}, name:{}, filename:{}, data:{}", url, name, filename, JsonUtil.parseBean2Str(data));

        MultipartBody.Builder builder = new MultipartBody.Builder()
            .setType(MultipartBody.FORM)
            .addFormDataPart(name, filename, fileBody);

        Map<String, T> markedData = mark(data);
        if (markedData != null) {
            for (String key : markedData.keySet()) {
                builder.addFormDataPart(key, markedData.get(key).toString());
            }
        }

        return execute(
            this.builder
                .url(mark(url))
                .post(builder.build())
                .build()
        );

    }

    private <T> FormBody getFormBody(Map<String, T> data) {

        FormBody.Builder builder = new FormBody.Builder();

        if (data != null) {

            for (String key : data.keySet()) {
                T value = data.get(key);
                builder.add(key, value == null ? "" : value.toString());
            }

        }
//		logger.info("getFormBody() data: {}", JsonUtil.parseBean2Str(builder));
        return builder.build();
    }

    public byte[] doPost2(String url, String body) {
        logger.info("doPost2() url:{}, body:{}", url, body);
        Request request = builder
            .url(mark(url))
            .post(RequestBody.create(MediaType.parse("application/json; charset=UTF-8"), body))
            .build();
        try {
            Response response = clientBuilder.build().newCall(request).execute();
            if (response.isSuccessful()) {
                return response.body().bytes();
            }
        } catch (Exception e) {
            logger.info("doPost2()", e);
        }
        return null;
    }

    private String execute(Request request) {

        try {

            Response response = clientBuilder.build().newCall(request).execute();
            logger.info("execute() code: {}, message: {}", response.code(), response.message());
            if (!response.isSuccessful()) {
                return null;
            }

            ResponseBody body = response.body();
            if (body == null) {
                logger.info("execute() responseBody null!");
                return null;
            }

            String result = body.string();
//			logger.info("execute() url: {}, result: {}", request.url(), result);
            return result;

        } catch (IOException e) {
            logger.error("execute() url: {}", request.url(), e);
            return null;
        }
    }

    /**
     * @param url      下载连接
     * @param saveDir  储存下载文件的目录
     * @param filename 文件名
     * @param listener 下载监听
     */
    @SuppressWarnings("NullableProblems")
    public void download(final String url, final String saveDir, final String filename, final OnDownloadListener listener) {
        clientBuilder.build().newCall(builder.url(url).build())
            .enqueue(new Callback() {

                @Override
                public void onFailure(Call call, IOException e) {
                    // 下载失败
                    if (listener != null) {
                        listener.onDownloadFailed(-1, e.getMessage());
                    }
                }

                @Override
                public void onResponse(Call call, Response response) {

                    // 储存下载文件的目录
                    File dir = FileUtil.mkdir(saveDir);
                    if (dir == null) {
                        if (listener != null) {
                            listener.onDownloadFailed(-1, "create saveDir failed! dir: " + saveDir);
                        }
                        return;
                    }

                    InputStream is = null;
                    FileOutputStream fos = null;
                    byte[] buf = new byte[2048];
                    int len;
                    try {
                        is = response.body().byteStream();
                        long total = response.body().contentLength();
                        File file = new File(saveDir, filename);
                        fos = new FileOutputStream(file);
                        long sum = 0;
                        while ((len = is.read(buf)) != -1) {
                            fos.write(buf, 0, len);
                            sum += len;
                            int progress = (int) (sum * 1.0f / total * 100);

                            // 下载中
                            if (listener != null) {
                                listener.onDownloading(progress);
                            }
                        }
                        fos.flush();
                        // 下载完成
                        if (listener != null) {
                            listener.onDownloadSuccess(file.getAbsolutePath());
                        }
                    } catch (Exception e) {
                        if (listener != null) {
                            listener.onDownloadFailed(-1, e.getMessage());
                        }
                    } finally {
                        try {
                            if (is != null)
                                is.close();
                        } catch (IOException e) {
                            logger.error("HttpUtil download 关闭输入流异常", e);
                        }
                        try {
                            if (fos != null)
                                fos.close();
                        } catch (IOException e) {
                            logger.error("HttpUtil download 关闭文件输出流异常", e);
                        }
                    }
                }
            });
    }

    public interface OnDownloadListener {

        /**
         * 下载成功
         */
        void onDownloadSuccess(String path);

        /**
         * @param progress 下载进度
         */
        void onDownloading(int progress);

        /**
         * 下载失败
         */
        void onDownloadFailed(int code, String msg);
    }

}
