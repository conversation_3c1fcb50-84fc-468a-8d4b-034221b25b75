package com.ke.chat.tag.api.dto.application;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ModelSelectedDTO {

    @NotEmpty(message = "模型选择不能为空")
    @Size(max = 3, message = "最多只能选择3个模型")
    private List<String> models;

}
