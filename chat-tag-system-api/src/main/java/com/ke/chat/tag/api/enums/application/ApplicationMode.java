package com.ke.chat.tag.api.enums.application;

import com.ke.chat.tag.api.dto.application.WorkflowDTO;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

@AllArgsConstructor
@Getter
@SuppressWarnings("all")
public enum ApplicationMode {

    LLM("llm"), CHATFLOW("chatflow"),CHATFLOW_BASE_QA("chatflowBaseQa");
    private final String name;

    public WorkflowDTO.WorkflowMode mappingWorkflowMode() {
        return LLM.equals(this) ? WorkflowDTO.WorkflowMode.WORKFLOW : WorkflowDTO.WorkflowMode.ADVANCED_CHAT;
    }

    public static ApplicationMode of(String mode) {
        return Arrays.stream(ApplicationMode.values()).filter(m -> m.getName().equals(mode)).findFirst().orElseThrow(() -> new IllegalArgumentException("unknown mode: " + mode));
    }
}
