package com.ke.chat.tag.api.enums.file;

import lombok.Getter;

/**
 * function:
 *
 * <AUTHOR>
 */
@Getter
public enum FileAccessModeEnum {

    INTERNAL_NETWORK("internal_network", "内网"),
    EXTERNAL_NETWORK("external_network", "外网");

    private String code;

    private String description;

    FileAccessModeEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }
}
