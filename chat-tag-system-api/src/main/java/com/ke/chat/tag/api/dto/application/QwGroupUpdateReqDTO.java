package com.ke.chat.tag.api.dto.application;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class QwGroupUpdateReqDTO {
    @NotNull
    private Long applicationId;
    @NotNull
    private String qwGroupId;
    @NotBlank
    private String qwGroupName;
}
