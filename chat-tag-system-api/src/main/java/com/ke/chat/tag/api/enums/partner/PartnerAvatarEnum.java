package com.ke.chat.tag.api.enums.partner;

import lombok.Getter;

/**
 * function:
 *
 * <AUTHOR>
 */
@Getter
public enum PartnerAvatarEnum {

    STANDARD("standard", "标准头像"),

    SQUARE("square", "广场页头像"),

    MANAGE("manage", "管理页头像");

    private String code;

    private String name;

    PartnerAvatarEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }
}
