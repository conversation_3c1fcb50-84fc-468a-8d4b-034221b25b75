package com.ke.chat.tag.api.enums;

import lombok.Getter;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: yumiao05
 * @Date: 2025/04/15/11:33
 * @Description:
 */
@Getter
public enum RecordResultEnum {

    SUCCESS("success","成功"),

    FAIL("failed","失败"),

    BREAK("break","中断")

    ;


    String code;

    String name;

    RecordResultEnum(String code, String name){
        this.code = code;
        this.name = name;

    }
    public static String getNameByCode(String code){
        for (RecordResultEnum result : RecordResultEnum.values()) {
            if (result.code.equals(code)) {
                return result.name;
            }
        }
        return "";
    }


}
