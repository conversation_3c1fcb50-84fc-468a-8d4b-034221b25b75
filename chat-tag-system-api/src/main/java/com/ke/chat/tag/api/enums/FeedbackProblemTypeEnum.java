package com.ke.chat.tag.api.enums;

/**
 * Description: FeedbackProblemTypeEnum
 * Author: xiaoshudi001
 * Date: 2023/11/22
 */
public enum FeedbackProblemTypeEnum {
    /**
     * 问题定位类型
     */
    FEEDBACK_CODE_RECORD(1, "反馈记录"),
    PROBLEM_IDENTIFICATION_CODE_RECORD(2, "反馈问题定位记录");

    private Integer code;
    private String name;

    FeedbackProblemTypeEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public static FeedbackProblemTypeEnum getByCode(Integer code) {
        for (FeedbackProblemTypeEnum feedbackProblemTypeEnum : FeedbackProblemTypeEnum.values()) {
            if (feedbackProblemTypeEnum.getCode().equals(code)) {
                return feedbackProblemTypeEnum;
            }
        }
        return null;
    }

    public Integer getCode() {
        return code;
    }

    public String getName() {
        return name;
    }
}
