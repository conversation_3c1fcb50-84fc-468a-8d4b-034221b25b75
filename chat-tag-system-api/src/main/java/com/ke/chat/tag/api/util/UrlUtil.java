package com.ke.chat.tag.api.util;

import java.io.UnsupportedEncodingException;
import java.net.MalformedURLException;
import java.net.URL;
import java.net.URLDecoder;

/**
 * <AUTHOR>
 */
public class UrlUtil {
    public static String getDecodedBasicUrl(String urlStr) throws MalformedURLException, UnsupportedEncodingException {
        URL url = new URL(urlStr);
        String protocol = url.getProtocol();
        String host = url.getHost();
        String path = url.getPath();
        String urlResult = protocol + "://" + host + path;
        return URLDecoder.decode(urlResult, "utf-8");
    }

}
