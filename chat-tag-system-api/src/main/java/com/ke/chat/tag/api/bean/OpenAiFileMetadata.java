package com.ke.chat.tag.api.bean;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class OpenAiFileMetadata {

    /**
     * 后处理器，包括两种：file_indexing/file_parse
     */
    @JsonProperty("post_processors")
    private List<String> postProcessors;

    @JsonProperty("city_list")
    private List<String> cityList;

    private List<String> callbacks;

    private String user;
}
