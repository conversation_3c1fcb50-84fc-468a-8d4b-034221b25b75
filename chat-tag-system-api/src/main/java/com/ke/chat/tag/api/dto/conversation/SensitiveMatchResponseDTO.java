package com.ke.chat.tag.api.dto.conversation;

import com.ke.chat.tag.api.dto.sensitive.SensitiveMatchInfoDTO;
import com.ke.chat.tag.api.dto.sensitive.SensitiveMatchInfoDTO.DataDetail;
import lombok.Data;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/5/23 3:07 下午
 **/
@Data
public class SensitiveMatchResponseDTO {
    // 是否命中敏感词
    private boolean matched;
    private List<String> matchedWords;

    public SensitiveMatchResponseDTO(SensitiveMatchInfoDTO sensitiveMatchInfoDTO) {
        if (sensitiveMatchInfoDTO == null || sensitiveMatchInfoDTO.getData() == null) {
            this.matched = false;
            this.matchedWords = Collections.emptyList();
        } else {
            matched = sensitiveMatchInfoDTO.getData().getCount() > 0;
            matchedWords = sensitiveMatchInfoDTO.getData().getDetail().stream().map(DataDetail::getWord).collect(Collectors.toList());
        }
    }
}
