package com.ke.chat.tag.api.dto.knowledge;

import com.ke.chat.tag.api.validator.annotation.StringListElementLength;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Size;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class QwChatQaPairModifyRequestDTO {
    private Long id;
    private Long applicationId;

    @Size(min = 1, max = 200, message = "提问字数需为1-200字，请调整。")
    private String question;

    @Size(min = 1, max = 20000, message = "答案字数需为1-20000字，请调整。")
    private String answer;

    @Size(max = 20, message = "tagList不能超过20个")
    @StringListElementLength(maxLength = 32, message = "每个标签最多不能超过32个字符")
    private List<String> tagList;

    @Size(max = 20, message = "cityList不能超过20个")
    private List<String> cityList;
}
