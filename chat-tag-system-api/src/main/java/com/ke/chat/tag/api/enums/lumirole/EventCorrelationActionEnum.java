package com.ke.chat.tag.api.enums.lumirole;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 事件形成的动作的枚举
 *
 * <AUTHOR>
 * @date 2025/02/11
 */
public enum EventCorrelationActionEnum {

    CLIENT_VAD("VAD静音检测",
        RealtimeInteractionEventEnum.INPUT_AUDIO_BUFFER_PRE_TEN_PACKAGE_COMMIT, RealtimeInteractionEventEnum.INPUT_AUDIO_BUFFER_COMMIT),
    CLIENT_A_SENTENCE_TAKES("客户端感受的一句话耗时",
        RealtimeInteractionEventEnum.INPUT_AUDIO_BUFFER_COMMIT, RealtimeInteractionEventEnum.INPUT_AUDIO_BUFFER_APPEND_FIRST),
    CLIENT_DEVICE_PLAY("设备播放",
        RealtimeInteractionEventEnum.DEVICE_READY_PLAY, RealtimeInteractionEventEnum.DEVICE_PLAY),

    SERVER_ASR_POST_PACKET("ASR尾包",
        RealtimeInteractionEventEnum.INPUT_AUDIO_BUFFER_COMMITTED, RealtimeInteractionEventEnum.INPUT_AUDIO_TRANSCRIPT_DONE),
    SERVER_A_SENTENCE_TAKES("服务端一句话耗时",
        RealtimeInteractionEventEnum.INPUT_AUDIO_TRANSCRIPT_DONE, RealtimeInteractionEventEnum.RESPONSE_AUDIO_TTS_CONNECT),
    SERVER_CHAT_LLM_A_SENTENCE_TAKES("CHAT LLM一句话耗时",
        RealtimeInteractionEventEnum.CHAT_PREPARE, RealtimeInteractionEventEnum.CHAT_FIRST_SENTENCE),
    SERVER_CHAT_FIRST_PACKAGE("服务端首包耗时",
        RealtimeInteractionEventEnum.CHAT_PREPARE, RealtimeInteractionEventEnum.CHAT_BEGIN),
    SERVER_TTS_FIRST_PACKET("TTS首包",
        RealtimeInteractionEventEnum.RESPONSE_AUDIO_TTS_CONNECT, RealtimeInteractionEventEnum.RESPONSE_AUDIO_DELTA_FIRST);

    private final String actionDescription;

    private final RealtimeInteractionEventEnum beforeEvent;

    private final RealtimeInteractionEventEnum afterEvent;

    /**
     * key: afterEvent
     * value: 对应的EventCorrelationActionEnum列表
     */
    private static final Map<RealtimeInteractionEventEnum, List<EventCorrelationActionEnum>> event2ActionMap = new HashMap<>();

    static {
        for (EventCorrelationActionEnum value : EventCorrelationActionEnum.values()) {
            event2ActionMap.computeIfAbsent(value.afterEvent, k -> new ArrayList<>()).add(value);
        }
    }

    EventCorrelationActionEnum(String actionDescription, RealtimeInteractionEventEnum beforeEvent, RealtimeInteractionEventEnum afterEvent) {
        this.actionDescription = actionDescription;
        this.beforeEvent = beforeEvent;
        this.afterEvent = afterEvent;
    }

    public static List<EventCorrelationActionEnum> getActionListByAfterEvent(RealtimeInteractionEventEnum afterEvent) {
        // 如果 event2ActionMap 中没有对应的键，返回一个空集合
        return event2ActionMap.getOrDefault(afterEvent, Collections.emptyList());
    }


    public String getActionDescription() {
        return actionDescription;
    }

    public RealtimeInteractionEventEnum getBeforeEvent() {
        return beforeEvent;
    }

    public RealtimeInteractionEventEnum getAfterEvent() {
        return afterEvent;
    }
}
