package com.ke.chat.tag.api.enums.application;

import lombok.Getter;

@Getter
public enum AudioAskStatusEnum {

    //初始化
    INIT("init", 1,"初始化"),
    //建立连接
    CONNECTED("connected", 2,"建立连接"),
    //记录参数
    RECORD_PARAMS("record_params", 3,"记录参数"),
    //开始录音
    STARTED("started", 4,"开始录音"),
    //录音中
    RECORDING("recording", 5,"录音中"),
    //录音结束中
    STOPPING("stopping",6, "录音结束中"),
    //录音结束
    STOPPED("stopped",7, "录音已结束"),
    // ASR空
    ASR_EMPTY_DEFAULT_REPLY("asrEmptyDefaultReply", 8,"ASR结果为空默认回复"),
    //模型调用
    MODEL_ASK("model_ask", 8,"模型调用"),
    //转语音
    TTS("tts", 9,"转语音"),
    //结束
    COMPLETED("completed", 10,"结束"),

    ERROR("error", -1,"错误"),

    ONLY_ASR("onlyAsr", 11,"仅调用ASR"),
    //ASR结果
    ASR_RESULT("asrResult", 12,"ASR结果"),

    /**
     * 只返回给前端，表示已生成记录，不记录在后端状态中
     */
    RECORD_SUCCESS("recordSucess", 13,"录音成功"),

    ;

    AudioAskStatusEnum(String status, int index, String description) {
        this.status = status;
        this.index = index;
        this.description = description;
    }

    private final String status;
    private final int index;
    private final String description;
}
