package com.ke.chat.tag.api.enums.exam;

import lombok.Getter;

@Getter
public enum ExamEvaluateEnum {

    USEFUL((byte) 1, "直接可用"),
    NEED_MODIFY((byte) 2, "修改可用"),
    UNUSABLE((byte) 3, "不可用");

    private Byte evaluateCode;
    private String evaluateDesc;

    ExamEvaluateEnum(Byte evaluateCode, String evaluateDesc) {
        this.evaluateCode = evaluateCode;
        this.evaluateDesc = evaluateDesc;
    }
}
