package com.ke.chat.tag.api.dto.application;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import com.ke.chat.tag.api.dto.home.base.ResignUrl;
import com.ke.chat.tag.api.dto.knowledge.KnowledgeFileResponseDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.time.LocalDateTime;
import java.util.List;
import java.util.function.Function;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@SuperBuilder
//@JsonInclude(JsonInclude.Include.NON_NULL)
public class ApplicationInfo implements ResignUrl {

    /**
     * 主键
     */
    private Long applicationId;
    private Long innerId;
    /**
     * 应用来源
     */
    private String source;

    /**
     * 应用名称
     */
    private String name;

    /**
     * 应用介绍
     */
    private String description;

    /**
     * 图标url
     */
    private String icon;

    /**
     * 应用类型 knowledge: 知识库；role：角色
     */
    private String type;

    /**
     * 点赞数
     */
    private Long likes;

    /**
     * 热度
     * 互动量 * 10
     */
    private String heat;

    /**
     * 后端排序用
     * 热度
     */
    private Long heatTemp;

    /**
     * 该用户是否点赞过
     */
    private boolean liked;

    /**
     * 该用户是否可编辑
     */
    private boolean editable;

    /**
     * 用户量
     */
    private Long uv;

    /**
     * 创建时间
     */
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime createTime;

    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime updateTime;

    /**
     * 创建时间
     */
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime exposureTime;

    /**
     * 创建用户昵称
     */
    private String nickName;

    /**
     * 检索相似度
     */
    private Float searchSimilarity;

    /**
     * 检索数量
     */
    private Integer searchNum;

    /**
     * 空检索回复
     */
    private String emptySearchReply;

    /**
     * 收藏数
     * 注：webapp（即广告位应用）暂不支持
     */
    private Integer collects;

    /**
     * 本人是否收藏
     * 注：webapp（即广告位应用）暂不支持
     */
    private boolean collected;

    /**
     * 应用的标签
     */
    private List<String> tags;

    /**
     * 存在草稿尚未发布
     */
    private int isDraftUnpublished;

    private String groupName;

    private String groupType;

    /**
     * 创建人ucid
     */
    private String cuid;

    /**
     * 创建人姓名
     */
    private String cname;

    /**
     * 最后一次修改人ucid
     */
    private String muid;

    /**
     * 最后一次修改人姓名
     */
    private String mname;

    List<KnowledgeFileResponseDTO> knowledgeFileResponseDTOList;

    private Integer countQA;

    private Integer csvNum;
    private Integer pdfNum;
    private Integer docNum;
    private Integer wikiNum;
    private Integer mdNum;

    /**
     * 应用所有者ucId
     */
    private String ownerUserId;

    /**
     * 应用所有者系统号
     */
    private String ownerUserCode;

    /**
     * 应用使用的模型
     */
    private String model;

    /**
     * 模式
     */
    private String mode;

    /**
     * 是智能体拥有者（true表示是，false表示不是）
     */
    @JsonProperty("isOwnerUser")
    private boolean isOwnerUser;

    /**
     * 空间编码
     */
    private String spaceCode;

    /**
     * 存在无效绑定资源 【true表示存在无效绑定资源  false表示不存在无效绑定资源】
     */
    private boolean existInvalidBind;

    private List<String> toolNameList;

    private String orgName;

    private boolean dataButtonHidden = false;

    @Override
    public void resign(Function<String, String> resignFunc) {
        this.setIcon(resignFunc.apply(icon));
    }
}
