package com.ke.chat.tag.api.dto.knowledge;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.annotation.Nullable;
import javax.validation.constraints.NotEmpty;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class KnowledgeWikiDTO {

    @NotEmpty
    private String wikiLinks;

    @Nullable
    private String visibility;

    private String dir;

    private Boolean sensitive;

}
