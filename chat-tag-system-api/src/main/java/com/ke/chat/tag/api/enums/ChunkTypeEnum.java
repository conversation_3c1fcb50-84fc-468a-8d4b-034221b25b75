package com.ke.chat.tag.api.enums;

import java.util.Arrays;
import java.util.Optional;

public enum ChunkTypeEnum {

    DEFAULT("default", "段落", 1),
    QA("qa", "QA", 1),
    SECTION("section", "段落", 2);

    public final String code;

    public final String type;

    // 向量库chunk唯一id生成影响因子(针对同一切片不同类型)
    public final Integer idGenerateFactor;

    ChunkTypeEnum(String code, String type, Integer idGenerateFactor) {
        this.code = code;
        this.type = type;
        this.idGenerateFactor = idGenerateFactor;
    }

    public static String parseType(String code) {
        Optional<ChunkTypeEnum> op = Arrays.stream(ChunkTypeEnum.values())
            .filter(i -> i.getCode().equals(code))
            .findFirst();
        return op.orElse(DEFAULT).getType();
    }

    public static String parseTag(String type) {
        Optional<ChunkTypeEnum> op = Arrays.stream(ChunkTypeEnum.values())
            .filter(i -> i.getType().equals(type))
            .findFirst();
        return op.orElse(DEFAULT).getCode();
    }

    public static int getFactorByCode(String code) {
        return Arrays.stream(ChunkTypeEnum.values())
            .filter(i -> i.getCode().equals(code))
            .map(i -> i.getIdGenerateFactor())
            .findFirst().orElse(1);
    }

    public String getType() {
        return type;
    }

    public String getCode() {
        return code;
    }

    public int getIdGenerateFactor() {
        return idGenerateFactor;
    }

    public static boolean isTextType(String type) {
        return DEFAULT.getCode().equals(type.toLowerCase()) || SECTION.getCode().equals(type.toLowerCase());
    }

    public static boolean isQaType(String type) {
        return QA.getCode().equals(type.toLowerCase());
    }
}
