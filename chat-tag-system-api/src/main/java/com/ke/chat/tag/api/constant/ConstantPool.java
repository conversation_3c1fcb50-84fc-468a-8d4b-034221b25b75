package com.ke.chat.tag.api.constant;

/**
 * 常量池
 *
 * <AUTHOR>
 */
public class ConstantPool {

    public static final String SERVICE_ID = "chat-tag-system";

    public static final Integer pageNum = 1;

    public static final Integer pageSize = 20;

    public static final Integer maxPageSize = 100;

    public static final String CHECK_PASS = "check_pass";

    public static final int MAX_CORE_POOL_SIZE = 20;

    public static final String OPENAPI_DEFAULT_USER = "1000000029406069";

    // todo 当前提示词模板固定为rag模板，后续增加多模板id
    public static final String RAG_TEMPLATE_ID = "rag_template";

    /**
     * 当前取值16777216 为String对象大小为1M时对象中length的长度值 ,会替换logOutSize 的值
     */
    public static final int MAX_LOG_OUT_SIZE = 524288;

    public static final String JSON_HEADER = "application/json";

    public static final String QYQK_WHITE_LIST = "QYQK_WHITE_LIST";

    public static final String AUDIO = "audio";

    public static final Long TEN_YEARS = 10L * 24L * 3600L * 365L;

    public static final Long SEVEN_DAYS = 24L * 3600L * 7L;

    public static final Long THIRTY_DAYS = 24L * 3600L * 30L;

    public static final String SWITCH_TURN_OFF = "turnOff";

    public static final String BELLA_TEAM_PERMISSIONS = "bella_team_permissions";

    public static class AskConfigConstant {
        public static final String SSE_CALLBACK = "sseCallback";
        public static final String SSE_RUN_END = "runEndFuture";
        public static final String TOOL_RUN_END = "toolEndFuture";
        public static final String SSE_RUN_START = "runStartFuture";
        public static final String SSE_RUN_RESULT = "runResult";
        public static final String SSE_QA_RESPONSE = "qaResponse";
        public static final String SSE_STREAM_BUFFER = "streamBuffer";
        public static final String SSE_MSG_COMPLETE = "completeMsg";

        public static final String SSE_RUN_ERROR = "error";

        public static final String SSE_REASONING_ELAPSED = "reasoningElapsed";
    }

    /**
     * 分享学习的固定UUID
     */
    public final static String UUID_SHARE = "robot_share_uuid";
    public static class BellaApiConstant {
        private BellaApiConstant() {
        }
        public static final String BELLA_TOKEN = "bellaToken";  // bella api访问token
        public static final String AUTHORIZATION = "Authorization";    // open api模型调用ak
        public static final String PLATFORM_ID = "platformId";  // 调用平台id
    }

    private ConstantPool() {
    }

    public static final String HEADER = "header";
    public static final String TASK_ID = "task_id";


    public static class SentinelResources {
        private SentinelResources() {
        }
        public static final String BELLA_API_INTERFACE = "bella_api_interface";
    }

    public static class KeRagChunkExtras {

        private KeRagChunkExtras() {
        }

        public static final String EXTRA_KEY_STATUS = "status"; // 切片状态

        public static final String HIDDEN = "hidden";   // 切片下线，可展示无法检索

        public static final String ONLINE = "online";   // 切片上线，可展示可检索

        public static final String DROPPED = "dropped"; // 切片删除，无法展示无法检索

        public static final String EXTRA_KEY = "extra"; // 切片状态

    }

    public static class MessageConstant {

        private MessageConstant() {
        }

        public static final String MESSAGE_METADATA_SOURCE = "source"; // message来源

    }

    public static final String WORKFLOW_SUCCESS_STATUS = "succeeded";

    public static final String BELLA_WORKFLOW_TENANT_ID = "04633c4f-8638-43a3-a02e-af23c29f821f";

    public static class PartnerConstant {
        private PartnerConstant() {
        }
        public static final String TASK_ID = "taskId";
        public static final String TASK_NAME = "taskName";

        public static final String SAVE_TIME = "saveTime";
        public static final String ORDER_NUM = "orderNum";
    }

    public static final String USER_INFO_ATTR = "USER_INFO";

}
