package com.ke.chat.tag.api.dto.application.tag;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class FlowTagsResponseDTO {
    /**
     * 可复制智能体标签列表
     */
    private List<TagInfo> enableCopyAppTags;
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TagInfo {
        private String tag;
        private String name;
        private String desc;
        private String tagGroup;
        //这个字段主要标识是否可以在智能体保存页面展示
        private Boolean editable = true;
        //这个字段标识是否在广场也筛选展示
        private Boolean show = true;

    }

}
