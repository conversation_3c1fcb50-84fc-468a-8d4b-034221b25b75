package com.ke.chat.tag.api.util;

import com.amazonaws.util.StringUtils;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;

import java.io.IOException;

/**
 * 网页解析
 */
@Slf4j
public class JsoupUtil {

    public static String title(String url) throws IOException {
        LOGGER.info("title() url:{}", url);
        Document doc = Jsoup.connect(url).get();
        String title = doc.title();
        if (StringUtil.isEmpty(title)) {
            title = doc.getElementsByAttributeValue("property", "og:title").attr("content");
        }
        return title;
    }

    public static String text(String htmlStr) {

        LOGGER.info("text()");
        if (StringUtils.isNullOrEmpty(htmlStr)) {
            LOGGER.info("text() htmlStr is null or empty!");
            return "";
        }
        Document doc = Jsoup.parse(htmlStr);
        return doc.text();
    }

    public static void main(String[] args) {
        String url = "https://mp.weixin.qq.com/s/pVW2X6aSm4hjIwvq6HHmCA";
        try {
            String title = title(url);
            System.out.println(title);
        } catch (IOException e) {
            e.printStackTrace();
        }
        System.out.println("OK");
    }

}
