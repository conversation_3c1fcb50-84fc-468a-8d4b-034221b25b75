package com.ke.chat.tag.api.enums.application;

import lombok.Getter;

@Getter
public enum AgentAskRequestSourceEnum {

    BELLA_PLATFORM("bellaPlatform", "bella平台包含pc端与小程序端"),

    BELLA_API("bellaApi", "bella的智能体api接口"),

    QW_BOT("qwBot", "企微机器人回调");

    private final String code;
    private final String desc;

    AgentAskRequestSourceEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}
