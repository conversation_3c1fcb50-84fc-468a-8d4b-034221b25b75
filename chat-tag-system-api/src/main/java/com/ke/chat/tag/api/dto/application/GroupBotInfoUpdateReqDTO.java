package com.ke.chat.tag.api.dto.application;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class GroupBotInfoUpdateReqDTO {
    @NotNull
    private Integer botId;

    @NotBlank
    private String botName;
}
