package com.ke.chat.tag.api.dto.application;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.annotation.Nullable;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ApplicationValidationReqDTO {

    @Nullable
    private Long applicationId;

    @NotNull(message = "applicationName must not be blank")
    private String name;

    /**
     * 是智能体拥有者（true表示是，false表示不是）
     */
    @JsonProperty("isOwnerUser")
    private boolean isOwnerUser;

}
