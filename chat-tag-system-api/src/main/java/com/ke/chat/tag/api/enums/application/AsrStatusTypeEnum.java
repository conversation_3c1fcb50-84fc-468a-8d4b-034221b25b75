package com.ke.chat.tag.api.enums.application;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum AsrStatusTypeEnum {

    INTERMEDIATE("Intermediate", "TranscriptionResultChanged", "一句话的中间结果，内容会变"),
    SENTENCE_END("SentenceEnd", "SentenceEnd", "一句话的最终结果"),
    COMPLETED("Completed", "TranscriptionCompleted", "翻译结束"),
    ;

    private String type;
    private String asrType;
    private String discription;

    public static AsrStatusTypeEnum getEnumByAsrType(String asrType) {
        for (AsrStatusTypeEnum value : AsrStatusTypeEnum.values()) {
            if (value.asrType.equals(asrType)) {
                return value;
            }
        }
        return null;
    }
}
