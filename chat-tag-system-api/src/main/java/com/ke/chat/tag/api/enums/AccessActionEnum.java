package com.ke.chat.tag.api.enums;

import lombok.Getter;

import java.util.Arrays;

@Getter
public enum AccessActionEnum {

    DISPATCH("dispatch", "分配子ak", false),
    LOAD("load", "加载子ak", true);


    AccessActionEnum(String action, String desc, boolean isBefore) {
        this.action = action;
        this.desc = desc;
        this.isBefore = isBefore;
    }

    private String action;
    private String desc;
    private boolean isBefore; // 是否前置动作

    public static AccessActionEnum getByAction(String action) {

        return Arrays.stream(AccessActionEnum.values())
            .filter(e -> e.action.equals(action))
            .findFirst().orElse(null);
    }
}
