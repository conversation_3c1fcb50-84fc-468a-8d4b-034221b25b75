package com.ke.chat.tag.api.dto.application;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class MainFrameDisplayTool {

    /**
     * 工具名称
     */
    private String toolName;
    /**
     * 工具中文名称
     */
    private String toolCnName;
    /**
     * 在主框中和前端交互的工具按钮code
     */
    private String toolCode;

    private Integer order;
}
