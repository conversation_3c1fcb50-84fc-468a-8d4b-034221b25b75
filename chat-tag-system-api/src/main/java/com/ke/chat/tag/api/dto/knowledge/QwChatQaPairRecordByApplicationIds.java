package com.ke.chat.tag.api.dto.knowledge;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class QwChatQaPairRecordByApplicationIds {
    private Long id;
    private String question;
    private String answer;
    private String qwGroupId;
    private String qwGroupName;
    private String status;
    private String type;
    private String date;

    private Long applicationId;

    private String applicationName;

    private String senderName;

    private String senderId;

    private LocalDateTime createTime;

    private LocalDateTime updateTime;

    private List<String> tagList;

    private List<String> cityList;

    private String muid;

    private String updaterName;

    @JsonProperty("isOwnerUser")
    private Boolean isOwnerUser;

    private Boolean authFlag;
}
