package com.ke.chat.tag.api.enums.qyqk;

import lombok.AllArgsConstructor;

@AllArgsConstructor
public enum TaskRoleRelationConfigPropertyEnum {

    about("about", "背景"),
    header("header", "列表页头像"),
    role_bg("role_bg", "角色背景"),
    role_dialogues("role_dialogues", "角色台词"),
    role_style("role_style", "性格特点"),
    role_audio("role_audio", "角色语音"),
    role_resistances("role_resistances", "抗性问题"),
    role_opportunities("role_opportunities", "机会点"),
    task_bg("task_bg", "任务背景"),
    task_target("task_target", "任务目标"),
    task_suggestion("task_suggestion", "专业建议"),
    open_remark("open_remark", ""),
    answer("answer", "提问回复"),
    level("level", "难度等级"),
    bg("bg", "背景图"),
    name("name", ""),
    multi_agent_minimum_agree_num("multi_agent_minimum_agree_num", "最小同意数"),
    multi_agent_minimum_agree_resist_num("multi_agent_minimum_agree_resist_num", "最小resist同意数"),
    multi_agent_agree_conditions("multi_agent_agree_conditions", "同意条件"),
    multi_agent_common_conditions("multi_agent_common_conditions", "常见条件"),
    multi_agent_resist_conditions("multi_agent_resist_conditions", "抵抗条件"),
    multi_agent_background("multi_agent_background", "背景条件"),
    multi_agent_profile("multi_agent_profile", "事情描述"),
    multi_agent_uname("multi_agent_uname", "称呼"),
    agent_sentence_hook("agent_sentence_hook", "存在agent_sentence_hook为多智能体模式"),
    challenge_mission("challenge_mission", "挑战任务");


    public final String type;
    public final String desc;
}
