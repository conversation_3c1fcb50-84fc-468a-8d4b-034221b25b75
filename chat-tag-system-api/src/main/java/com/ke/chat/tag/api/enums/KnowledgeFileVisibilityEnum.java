package com.ke.chat.tag.api.enums;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2023/7/7 10:23 上午
 **/
public enum KnowledgeFileVisibilityEnum {
    //所有人可见
    PUBLIC("public"),
    //仅维护人可见
    PRIVATE("private"),
    //包含当前可见全部内容（公开和自己维护的）
    ALL("all");

    public final String code;

    private static Map<String, KnowledgeFileVisibilityEnum> map = new HashMap<>();

    static {
        for (KnowledgeFileVisibilityEnum visibilityEnum : KnowledgeFileVisibilityEnum.values()) {
            map.put(visibilityEnum.code, visibilityEnum);
        }
    }

    KnowledgeFileVisibilityEnum(String code) {
        this.code = code;
    }

    public static Optional<KnowledgeFileVisibilityEnum> getByCode(String code) {
        return Optional.ofNullable(map.get(code));
    }
}
