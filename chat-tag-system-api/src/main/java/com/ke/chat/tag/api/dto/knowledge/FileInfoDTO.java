package com.ke.chat.tag.api.dto.knowledge;

import com.ke.chat.tag.api.dto.application.ApplicationValidationReqDTO;
import com.ke.chat.tag.api.dto.team.res.SpaceDTO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class FileInfoDTO {

    private String fileId;

    private String fileName;

    private String dir;

    private List<String> cityList;

    private List<String> businessTags;

    private List<ApplicationValidationReqDTO> applicationList;

    private String updaterUcid;

    private String creatorUcid;

    private String createTime;

    private String updateTime;

    private String visibility;

    private String sensitiveLevel;

    private int countQa;

    private List<SpaceDTO> authSpaces;

}
