package com.ke.chat.tag.api.dto.application;

import org.jetbrains.annotations.Nullable;

import javax.validation.constraints.NotNull;

public class RequestMessage {


    public RequestMessage() {

    }

    public RequestMessage(String role, String content, String name) {
        this.role = role;
        this.content = content;
        this.name = name;
    }

    // one of the system, user, assistant, function
    @NotNull
    private String role;

    /**
     * content is required for all messages except assistant messages with function calls;
     */
    @Nullable
    private String content;

    /**
     * name is required if role is function, and it should be the name of the function whose response is in the content;
     */
    @Nullable
    private String name;

}
