package com.ke.chat.tag.api.dto.rag;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@Data
@AllArgsConstructor
@NoArgsConstructor
@SuperBuilder
public class RagResp {

    private String status;

    private String step;

    @JsonProperty("trace_id")
    private String traceId;

    public static boolean callSuccess(RagResp ragResp) {
        return ragResp.getStatus() == null || "success".equals(ragResp.getStatus());
    }

    public static boolean callFailed(RagResp ragResp) {
        return "failed".equals(ragResp.getStatus());
    }
}
