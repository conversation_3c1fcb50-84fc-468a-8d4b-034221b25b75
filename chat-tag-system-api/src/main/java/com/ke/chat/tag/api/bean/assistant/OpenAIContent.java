package com.ke.chat.tag.api.bean.assistant;

import com.amazonaws.util.json.Jackson;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.ke.chat.tag.api.enums.assistant.AssistantContentEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class OpenAIContent {



    private Integer index;

    /**
     * 类型 image_file or text or image_url audio_url
     * 注：file类型为bella自定义，非官方标准协议
     */
    private String type;

    /**
     * 内容：可以为string或Text结构体
     */
    private Object text;

    /**
     * 图片
     */
    @JsonProperty("image_file")
    private ImageFile imageFile;

    @JsonProperty("image_url")
    private ImageUrl imageUrl;

    @JsonProperty("audio_url")
    private AudioUrl audioUrl;

    /**
     * 音频
     */
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class AudioUrl {
        //音频s3 file id
        private String url;
        //文本
        @JsonProperty("audio_transcript")
        private String audioTranscript;

    }

    /**
     * 内容
     */
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class Text {

        private String value;

        private List<Annotation> annotations;
    }

    /**
     * images 类型时用到
     */
    @Data
    public static class Annotation {

        /**
         * Always file_citation.
         */
        private String type;

        /**
         * 消息内容中需要替换的文本
         */
        private String text;

        @JsonProperty("file_citation")
        private FileCitation fileCitation;

        @JsonProperty("file_path")
        private FileCitation filePath;

        @JsonProperty("start_index")
        private Integer startIndex;
        @JsonProperty("end_index")
        private Integer endIndex;

        /**
         * 用于存储score等引用信息
         */
        private Map<String, String> metadata;
    }

    @Data
    public static class FileCitation {

        @JsonProperty("file_id")
        private String fileId;

        private String quote;
    }

    @Data
    public static class ImageFile {
        @JsonProperty("file_id")
        private String fileId;

        @JsonProperty("file_url")
        private String fileUrl;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class ImageUrl {

        private String url;

        private String detail;
    }

    public OpenAIContent.Text text() {
        return Jackson.fromJsonString(Jackson.toJsonString(text), Text.class);
    }

    public OpenAIContent.AudioUrl audioUrl() {
        return Jackson.fromJsonString(Jackson.toJsonString(audioUrl), AudioUrl.class);
    }

    public static OpenAIContent buildFromImageUrl(String imageUrl) {
        ImageUrl image = new ImageUrl();
        image.setUrl(imageUrl);
        return OpenAIContent.builder().type(AssistantContentEnum.IMAGE_URL.getType()).imageUrl(image).build();
    }


    public static OpenAIContent buildFromText(String query) {
        Text userText = new Text();
        userText.setValue(query);
        return OpenAIContent.builder().type(AssistantContentEnum.TEXT.getType()).text(userText).build();
    }

}
