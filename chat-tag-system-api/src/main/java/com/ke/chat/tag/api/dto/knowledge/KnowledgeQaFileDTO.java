package com.ke.chat.tag.api.dto.knowledge;

import com.ke.chat.tag.api.enums.KnowledgeFileVisibilityEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.lang.Nullable;

import javax.validation.constraints.NotEmpty;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class KnowledgeQaFileDTO {
    @NotEmpty
    private List<KnowledgeQaPair> qaPairList;
    @NotEmpty
    private String qaFileName;
    private String dir;
    private boolean sensitive;
    @Nullable
    private String visibility = KnowledgeFileVisibilityEnum.PRIVATE.code;
    @Nullable
    private List<Long> applicationList;

    @NotEmpty(message = "spaceCode不能为空")
    private String spaceCode;
}
