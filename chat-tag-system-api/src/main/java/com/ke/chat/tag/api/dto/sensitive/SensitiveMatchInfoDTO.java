package com.ke.chat.tag.api.dto.sensitive;

import lombok.Data;

import java.util.List;

/**
 * 敏感词命中接口
 *
 * <AUTHOR>
 * @date 2023/3/13 11:26 上午
 **/
@Data
public class SensitiveMatchInfoDTO {

    /**
     * status：
     * STATUS_0(0,"success"),
     * STATUS_1(1,"argument is missing"),
     * STATUS_2(2,"key is invalid"),
     * STATUS_3(3,"sign is invalid"),
     * STATUS_4(4,"service error");
     */
    private Integer status;

    private String message;

    private InnerData data;

    @Data
    public static class InnerData {
        //敏感词个数
        private Integer count;
        //需要进行敏感词匹配的文本
        private String text;
        private List<DataDetail> detail;

    }

    @Data
    public static class DataDetail {
        //敏感词在文本中出现的首个位置
        private Integer offset;

        //敏感词的长度
        private Integer length;

        //敏感词长度
        private String word;

        private String dicId;

        private Integer wordType;

        private String wordRule;

    }

}
