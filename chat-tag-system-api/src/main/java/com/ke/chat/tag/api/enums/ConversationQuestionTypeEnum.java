package com.ke.chat.tag.api.enums;

import lombok.Getter;

import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @date 2023/3/4 12:51 下午
 **/
@Getter
public enum ConversationQuestionTypeEnum {
    CONVERSATION_FIRST_QUESTION("firstQuestion", "会话内首次提问"),
    CONVERSATION_NEW_QUESTION("newQuestion", "会话内新提问"),
    REQUEST_NEW_ANSWER("requestNewAnswer", "换个回答");

    public final String code;
    public final String desc;

    ConversationQuestionTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static ConversationQuestionTypeEnum getByCode(String code) {
        return Stream.of(ConversationQuestionTypeEnum.values()).collect(Collectors.toMap(ConversationQuestionTypeEnum::getCode, Function.identity(), (key1, key2) -> key1)).get(code);
    }
}
