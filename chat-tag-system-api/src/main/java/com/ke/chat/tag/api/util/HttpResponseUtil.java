package com.ke.chat.tag.api.util;

import lombok.extern.slf4j.Slf4j;

import javax.servlet.http.HttpServletResponse;
import java.io.PrintWriter;

/**
 * HttpResponseUtil
 *
 * <AUTHOR>
 * @date 2025/3/4
 */
@Slf4j
public class HttpResponseUtil {

    private HttpResponseUtil() {
    }

    public static void writeJson(String json, HttpServletResponse response) {
        PrintWriter out = null;
        try {
            response.setCharacterEncoding("UTF-8");
            response.setContentType("application/json; charset=utf-8");
            out = response.getWriter();
            out.write(json);
            out.flush();
        } catch (Exception e) {
            LOGGER.error("writeJson exception json:{}, msg:{}", json, e);
        } finally {
            if (out != null) {
                try {
                    out.close();
                } catch (Exception e) {
                    LOGGER.error("writeJson out close exception msg:{}", e);
                }
            }
        }
    }

}
