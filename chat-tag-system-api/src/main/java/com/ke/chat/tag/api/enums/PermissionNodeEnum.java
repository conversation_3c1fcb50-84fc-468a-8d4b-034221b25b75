package com.ke.chat.tag.api.enums;

import lombok.Getter;

@Getter
public enum PermissionNodeEnum {

    /**
     * bella总体账户
     */
    BELLA("bella", "bella总账户"),

    /**
     * 智能体维度
     */
    AGENT("agent", "智能体"),
    AGENT_ASK_WEB("agent_ask_web", "智能体网页端对话"),
    AGENT_ASK_WECHAT("agent_ask_wechat", "智能体企微端对话"),
    AGENT_ASK_API_PLATFORM("agent_ask_api_platform", "智能体api对话：第三方平台"),
    AGENT_ASK_API_PERSONAL("agent_ask_api_personal", "智能体api对话：个人调用"),

    /**
     * 对每一功能点分配全局唯一ak
     */
    EVALUATION("evaluation", "自动化评测"),
    GPT_BUILDER("gpt_builder", "生成智能体"),
    DRAW("draw", "生图"),
    IMAGE_PARSE("image_parse", "图片识别"),
    KN<PERSON>LEDGE_EXTRACTOR("knowledge_extractor", "知识提取"),
    EMBEDDING("embedding", "向量化");

    public final String code;

    public final String desc;

    PermissionNodeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}
