package com.ke.chat.tag.api.dto.application;

import com.ke.chat.tag.api.dto.home.base.ResignUrl;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;
import java.util.function.Function;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class WebApplicationListResp implements ResignUrl {

    private List<WebApplicationInfo> webApplicationInfoList;

    @Override
    public void resign(Function<String, String> resignFunc) {
        if (CollectionUtils.isEmpty(webApplicationInfoList)) {
            return;
        }
        webApplicationInfoList.forEach(i -> i.setImgUrl(resignFunc.apply(i.getImgUrl())));
    }
}
