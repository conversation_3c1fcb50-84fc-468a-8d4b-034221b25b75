package com.ke.chat.tag.api.util;

import org.apache.commons.io.FilenameUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.DigestUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * S3预览服务
 *
 * <AUTHOR>
 * @date 2023/8/3 12:44 下午
 **/
@Service
public class S3PreviewService {

    private String bucketName;
    private String previewEndPoint;
    private String previewAk;
    private String previewSk;

    public S3PreviewService(
        @Value("${chat.tag.previewak}") String previewak,
        @Value("${chat.tag.previewsk}") String prewiewsk,
        @Value("${s3.endPoint.preview}") String previewEndPoint,
        @Value("${s3.default.bucketName}") String bucketName) {
        this.bucketName = bucketName;
        this.previewEndPoint = previewEndPoint;
        this.previewAk = previewak;
        this.previewSk = prewiewsk;
    }

    //生成预览地址
    public String getPreviewUrl(String fileKey, long expirationSeconds) {
        String path = FilenameUtils.concat(bucketName, fileKey + "!m_convert,o_pdf_view");
        path = "/" + path;
        return signatureUrl(previewEndPoint, path, previewAk, previewSk, "" + expirationSeconds);
    }


    // 预览服务签名计算
    private String signatureUrl(String domain, String path, String ak, String sk, String exp) {
        Map<String, String> data = new HashMap<>();
        long time = Calendar.getInstance().getTimeInMillis() / 1000;
        data.put("ak", ak);
        data.put("exp", exp);
        data.put("path", path);    // 注意urldecode！！！注意urldecode！！！注意urldecode！！！
        data.put("ts", "" + time);
        Set<String> keySet = data.keySet();
        List<String> list = new ArrayList<>(keySet);
        Collections.sort(list);
        String verifyStr = "";
        for (String s : list) {
            verifyStr += s.trim() + "=" + data.get(s).trim() + "&";
        }
        verifyStr += "sk=" + sk;
        String sign = DigestUtils.md5DigestAsHex(verifyStr.getBytes());
        String params = "ak=" + data.get("ak") + "&exp=" + data.get("exp") + "&ts=" + data.get("ts") + "&sign=" + sign;
        return domain + path + '?' + params;
    }

    /**
     * 是否支持预览
     *
     * @param filePath
     * @return
     */
    public boolean isSupportPreview(String filePath) {
        //若文件名以.doc, .docx，则走预览服务
        List<String> supportPreviewSuffix = Arrays.asList(".doc", ".docx");
        return supportPreviewSuffix.stream().anyMatch(filePath::endsWith);

    }
}
