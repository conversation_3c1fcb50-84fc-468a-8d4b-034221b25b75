package com.ke.chat.tag.api.bean.assistant;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Optional;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class File {

    private String fileId;

    private String url;

    private FileMetadata metadata;


    @Data
    public static class FileMetadata {

        /**
         * 文件位置
         */
        private Integer index;

        /**
         * 文件大小
         */
        private String size;

        /**
         * 文件名
         */
        private String name;

        /**
         * 文件类型
         */
        private String type;
    }

    /**
     * 静态方法获取metadata.name，注意所有的判空，使用lambda
     */
    public String getFileName() {
        return Optional.ofNullable(this.getMetadata())
            .map(FileMetadata::getName)
            .orElse("");
    }

    /**
     * 静态方法获取metadata.type，注意所有的判空，使用lambda
     */
    public String getFileType() {
        return Optional.ofNullable(this.getMetadata())
            .map(FileMetadata::getType)
            .orElse("");
    }


}
