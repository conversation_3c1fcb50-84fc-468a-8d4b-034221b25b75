package com.ke.chat.tag.api.enums.application;

import lombok.Getter;

@Getter
public enum AudioAskCommandEnum {

    //开始录音
    START_ASR("startAsr", "开始录音"),
    //录音结束
    STOP_ASR("stopAsr", "录音结束"),
    //记录参数
    RECORD_PARAMS("recordParams", "记录参数"),
    //只调用asr
    ONLY_ASR("onlyAsr", "仅调用ASR"),
    //文本调用
    TEXT_ASK("textAsk", "文本调用"),
    //查询状态
    QUERY_STATUS("queryStatus", "查询状态"),
    ;

    AudioAskCommandEnum(String command, String description) {
        this.command = command;
        this.description = description;
    }

    private String command;
    private String description;
}
