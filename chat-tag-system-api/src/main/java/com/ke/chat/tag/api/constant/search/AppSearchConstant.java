package com.ke.chat.tag.api.constant.search;

/**
 * function: 智能体在ES索引中的字段信息
 *
 * <AUTHOR>
 */
public class AppSearchConstant {

    private AppSearchConstant() {
    }

    /**智能体索引中字段*/
    public static final String FIELD_STATUS = "status";                   //智能体状态-字段
    public static final Integer FIELD_STATUS_ONE = 1;                     //智能体状态-有效
    public static final String FIELD_VISIBILITY = "visibility";           //智能体可见性类型-字段
    public static final String FIELD_VISIBILITY_PUBLIC = "public";        //智能体可见性类型-公共的
    public static final String FIELD_LAUNCH = "launch";                   //智能体上架类型-字段
    public static final Integer FIELD_LAUNCH_ONE = 1;                     //智能体上架类型-上架
    public static final String FIELD_SPACE_CODE = "spaceCode";           //智能体空间信息（包含所属空间与授权空间）信息集合
    public static final String FIELD_NAME = "name";                       //智能体名称
    public static final String FIELD_DESCRIPTION = "description";         //智能体描述
    public static final String FIELD_ORG_TAGS = "orgTags";                //事业线标签
    public static final String FIELD_FUNCTION_TAGS = "functionTags";      //职能标签
    public static final String FIELD_UNIVERSAL_TAGS = "universalTags";    //通用标签
    public static final String FIELD_CITY_TAGS = "cityTags";              //城市标签
    public static final String FIELD_EXAMPLE_QUESTIONS = "exampleQuestions";   //推荐问
    public static final String FIELD_INNER_ID = "innerId";   //智能体内部id
    public static final String FIELD_OUTER_ID = "outerId";   //智能体外部id
    public static final String FIELD_CREATE_TIME = "createTime";   //智能体创建时间
    public static final String FIELD_HEAT = "heat";   //智能体热度
    public static final String ENABLE_COPY_APP_TAGS = "authTags";    //可复制智能体标签
    public static final String FIELD_MODE = "mode";   //智能体模式
    public static final String FIELD_COPY_COUNT = "copyCount";   //智能体复制次数

}
