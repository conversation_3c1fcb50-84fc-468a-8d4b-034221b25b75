package com.ke.chat.tag.api.enums.qyqk;

import lombok.Getter;

@Getter
public enum RoleConfigType {

    dialogues((byte) 1, "角色台词"),
    resistances((byte) 2, "抗性问题"),
    opportunities((byte) 3, "机会点"),
    scripts((byte) 4, "优秀话术"),
    start((byte) 5, "首句话"),
    slow((byte) 6, "慢语音"),
    ;

    private Byte type;
    private String desc;

    RoleConfigType(Byte type, String desc) {
        this.type = type;
        this.desc = desc;
    }

}
