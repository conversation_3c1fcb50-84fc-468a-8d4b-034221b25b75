<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <parent>
    <groupId>com.ke.chat.tag</groupId>
    <artifactId>chat-tag-system</artifactId>
    <version>1.0.0-SNAPSHOT</version>
  </parent>
  <modelVersion>4.0.0</modelVersion>

  <artifactId>chat-tag-system-api</artifactId>
  <dependencies>
    <dependency>
      <groupId>com.alibaba</groupId>
      <artifactId>fastjson</artifactId>
    </dependency>
    <dependency>
      <artifactId>risk-safety-common-util</artifactId>
      <groupId>com.ke.risk.safety.common</groupId>
      <version>${risk-safety-common-util.version}</version>
    </dependency>

    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-validation</artifactId>
    </dependency>

    <dependency>
      <groupId>com.ke.risk.safety.common</groupId>
      <artifactId>risk-safety-s3-util</artifactId>
      <version>1.0.4-SNAPSHOT</version>
    </dependency>
    <dependency>
      <groupId>org.springframework</groupId>
      <artifactId>spring-web</artifactId>
    </dependency>
    <dependency>
      <groupId>cn.hutool</groupId>
      <artifactId>hutool-all</artifactId>
      <version>5.8.0</version>
      <scope>compile</scope>
    </dependency>

    <dependency>
      <groupId>com.alibaba</groupId>
      <artifactId>easyexcel</artifactId>
      <version>3.3.4</version>
    </dependency>

    <dependency>
      <groupId>org.springframework</groupId>
      <artifactId>spring-web</artifactId>
    </dependency>

    <!--        <dependency>-->
    <!--            <groupId>com.amazonaws</groupId>-->
    <!--            <artifactId>aws-java-sdk-s3</artifactId>-->
    <!--            <version>1.11.24</version>-->
    <!--        </dependency>-->

    <dependency>
      <groupId>io.grpc</groupId>
      <artifactId>grpc-core</artifactId>
      <version>1.20.0</version>
      <scope>compile</scope>
    </dependency>

    <dependency>
      <groupId>io.github.openfeign</groupId>
      <artifactId>feign-core</artifactId>
    </dependency>

    <dependency>
      <groupId>org.springframework.cloud</groupId>
      <artifactId>spring-cloud-openfeign-core</artifactId>
    </dependency>

    <dependency>
      <groupId>io.github.openfeign</groupId>
      <artifactId>feign-slf4j</artifactId>
    </dependency>

    <dependency>
      <groupId>io.github.openfeign</groupId>
      <artifactId>feign-jackson</artifactId>
    </dependency>

    <dependency>
      <groupId>io.github.openfeign</groupId>
      <artifactId>feign-okhttp</artifactId>
    </dependency>

    <dependency>
      <groupId>io.github.openfeign</groupId>
      <artifactId>feign-hystrix</artifactId>
    </dependency>
      <dependency>
          <groupId>com.lianjia.openiam</groupId>
          <artifactId>openiam-client-core</artifactId>
          <version>1.5.2</version>
          <scope>compile</scope>
      </dependency>
    <dependency>
      <groupId>org.apache.tomcat.embed</groupId>
      <artifactId>tomcat-embed-core</artifactId>
    </dependency>
    <dependency>
      <!-- jsoup HTML parser library @ https://jsoup.org/ -->
      <groupId>org.jsoup</groupId>
      <artifactId>jsoup</artifactId>
      <version>1.16.1</version>
    </dependency>
    <!-- xml序列化 -->
    <dependency>
      <groupId>com.thoughtworks.xstream</groupId>
      <artifactId>xstream</artifactId>
      <version>1.4.19</version>
    </dependency>
    <dependency>
      <groupId>com.fasterxml.jackson.datatype</groupId>
      <artifactId>jackson-datatype-jsr310</artifactId>
    </dependency>
    <dependency>
      <groupId>com.ke.train</groupId>
      <artifactId>easy-search-boot-starter</artifactId>
    </dependency>
      <dependency>
          <groupId>com.lianjia.infrastructure</groupId>
          <artifactId>spring-boot-starter-redis</artifactId>
      </dependency>
  </dependencies>

  <properties>
    <maven-source-plugin.version>3.0.1</maven-source-plugin.version>
    <maven-javadoc-plugin.version>3.1.1</maven-javadoc-plugin.version>
  </properties>

  <build>
    <plugins>
      <plugin>
        <artifactId>maven-source-plugin</artifactId>
        <version>${maven-source-plugin.version}</version>
        <executions>
          <execution>
            <id>attach-sources</id>
            <phase>package</phase>
            <goals>
              <goal>jar-no-fork</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <artifactId>maven-javadoc-plugin</artifactId>
        <version>${maven-javadoc-plugin.version}</version>
        <executions>
          <execution>
            <id>attach-javadocs</id>
            <phase>package</phase>
            <goals>
              <goal>jar</goal>
            </goals>
          </execution>
        </executions>
        <configuration>
          <encoding>UTF-8</encoding>
          <charset>UTF-8</charset>
          <additionalJOption>-Xdoclint:none</additionalJOption>
        </configuration>
      </plugin>
    </plugins>
  </build>

</project>
