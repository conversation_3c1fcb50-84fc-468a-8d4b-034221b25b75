# Chat Tag System 本地部署指南

## 🎉 构建成功！

恭喜！您的Java后端项目已经成功构建和部署。以下是详细的部署信息和使用指南。

## 📋 项目信息

- **项目名称**: Chat Tag System
- **Java版本**: OpenJDK 1.8.0_452
- **构建工具**: Maven 3.9.10
- **主要模块**:
  - `chat-tag-system-api`: API接口模块
  - `chat-tag-system-dao`: 数据访问层
  - `chat-tag-system-service`: 业务逻辑层
  - `chat-tag-system-start`: 启动模块

## 🚀 快速启动

### 1. 启动应用
```bash
./start-app.sh
```

### 2. 手动启动（如果脚本有问题）
```bash
# 设置Java 8环境
export JAVA_HOME=/usr/local/opt/openjdk@8/libexec/openjdk.jdk/Contents/Home
export PATH="/usr/local/opt/openjdk@8/bin:$PATH"

# 启动应用
java -Xms1024m -Xmx2048m -jar chat-tag-system-start/target/chat-tag-system.jar --spring.profiles.active=passport,test
```

## 🔧 环境配置

### Java环境
- **Java版本**: OpenJDK 8
- **安装路径**: `/usr/local/opt/openjdk@8/`
- **JAVA_HOME**: `/usr/local/opt/openjdk@8/libexec/openjdk.jdk/Contents/Home`

### Maven配置
- **版本**: Apache Maven 3.9.10
- **特殊参数**: `-Dmaven.resolver.transport.http.blocker.disabled=true`

## 📁 构建产物

构建成功后生成的JAR文件：
```
chat-tag-system-api/target/chat-tag-system-api-1.0.0-SNAPSHOT.jar
chat-tag-system-dao/target/chat-tag-system-dao-1.0.0-SNAPSHOT.jar
chat-tag-system-service/target/chat-tag-system-service-1.0.0-SNAPSHOT.jar
chat-tag-system-start/target/chat-tag-system.jar  # 主启动JAR
```

## 🛠️ 重新构建

如果需要重新构建项目：

```bash
# 使用Java 8构建
./build-with-java8.sh
```

或者手动构建：
```bash
# 设置Java 8环境
export JAVA_HOME=/usr/local/opt/openjdk@8/libexec/openjdk.jdk/Contents/Home
export PATH="/usr/local/opt/openjdk@8/bin:$PATH"

# 清理和构建
mvn clean package -DskipTests -Dmaven.resolver.transport.http.blocker.disabled=true
```

## 🔍 问题排查

### 1. 如果遇到Java版本问题
确保使用Java 8：
```bash
java -version
# 应该显示: openjdk version "1.8.0_452"
```

### 2. 如果遇到端口占用
检查端口8080是否被占用：
```bash
lsof -i :8080
```

### 3. 如果遇到依赖问题
清理Maven缓存：
```bash
mvn dependency:purge-local-repository
mvn clean install -DskipTests
```

## 📝 配置文件

应用使用以下Spring配置文件：
- `passport`: 认证相关配置
- `test`: 测试环境配置

## 🌐 访问应用

应用启动后，可以通过以下方式访问：
- **默认端口**: 8080
- **健康检查**: http://localhost:8080/actuator/health
- **API文档**: http://localhost:8080/swagger-ui.html (如果配置了Swagger)

## 📊 监控和日志

- **日志位置**: 控制台输出和应用配置的日志文件
- **JVM参数**: `-Xms1024m -Xmx2048m`
- **监控**: 可以通过Spring Boot Actuator端点进行监控

## 🎯 下一步

1. **配置数据库**: 根据需要配置数据库连接
2. **配置外部服务**: 配置需要的外部API和服务
3. **环境变量**: 设置生产环境所需的环境变量
4. **SSL证书**: 如果需要HTTPS，配置SSL证书
5. **负载均衡**: 如果需要高可用，配置负载均衡

## 🆘 获取帮助

如果遇到问题，请检查：
1. Java版本是否正确 (必须是Java 8)
2. Maven依赖是否完整下载
3. 网络连接是否正常
4. 端口是否被占用
5. 配置文件是否正确

---

**构建时间**: $(date)
**构建状态**: ✅ 成功
**Java版本**: OpenJDK 1.8.0_452
**Maven版本**: Apache Maven 3.9.10
